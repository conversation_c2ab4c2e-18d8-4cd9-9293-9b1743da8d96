core:
  image:
    repository: us-central1-docker.pkg.dev/bks-ar-pkg/images-prd/core
  pdb:
    api:
      minAvailable: 90%
    grpcApi:
      minAvailable: 70%
    reportsApi:
      minAvailable: 90%
    searchApi:
      minAvailable: 70%


  replicaCount:
    celeryPriority: 8
    celeryPushWorker: 3
    celeryRegular: 2
    reportsApi: 10
    grpcApi: 4
    
  autoscaling:
    admin:
      enabled: true
      minReplicas: 5
      maxReplicas: 6
    api:
      enabled: true
      minReplicas: 10
      maxReplicas: 200
    celeryPriority:
      enabled: true
      minReplicas: 4
      maxReplicas: 10
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 15000
    celeryPush:
      enabled: true
      minReplicas: 1
      maxReplicas: 4
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celeryRegular:
      enabled: true
      minReplicas: 2
      maxReplicas: 8
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 15000
    celerySegment:
      enabled: true
      minReplicas: 1
      maxReplicas: 8
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 10000
    searchApi:
      enabled: true
      minReplicas: 5
      maxReplicas: 30
      CPUTargetUtilizationPercentage: 35

  variables:
    apiUwsgiProcesses: 4
    apiUwsgiLazyApp: False
    apiUwsgiForkHooks: True
    apiUwsgiRssReload: True
    grouponMeshNamespace: .groupon
    booksyVariant: live
    booksyCountryCode: us
    booksyRedisDB: 1
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    authHost: auth-server.auth.svc.cluster.local:8010
    grpcClientProxyHost: http://grpc-us-client-proxy.grpc-us.svc.cluster.local:9911
    postgresql: 
      masterHost: bks-prd-4-us-c1-pg-primary-lb-5432-cross-mesh.svc.cluster.local
      paymentsHost: **********
      draftsHost: **********
      LBslaveHosts: bks-prd-4-us-c1-pg-replica-lb-5432-cross-mesh.svc.cluster.local
      LBslaveReportsHosts: bks-prd-4-us-c1-pg-reports-replica-cross-mesh.svc.cluster.local
    elasticsearch:
      host: bks-prd-4-us-c1-es-lb-cross-mesh.svc.cluster.local
      port: 9200
      numerOfReplicas: 2
      numerOfShards: 5
    redis:
      celeryBackendHost: bks-prd-4-us-c1-redis-celery.svc.cluster.local
      celeryBeatHost: bks-prd-4-us-c1-redis-celery.svc.cluster.local
      celeryBrokerHost: bks-prd-4-us-c1-redis-celery.svc.cluster.local
      celeryBulkCacheHost: bks-prd-4-us-c1-redis-email-bulk.svc.cluster.local
      riverRedisHost: bks-prd-4-us-c1-redis-river.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: bks-prd-4-us-c1-redis-throttling.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: bks-prd-4-us-c1-redis-fifo.svc.cluster.local
      subdomainsRedisHost: bks-prd-4-us-c1-redis-core-subdomains.svc.cluster.local
      simplifiedBookingRedisHost: bks-prd-4-us-c1-redis-simplified-booking.svc.cluster.local

    boostClaims:
      GCSProjectID: bks-prd-4-us-c1

    searchApiUwsgiProcesses: 4
    reportsApiUwsgiProcesses: 4
    celeryPriorityProcesses: 15
    celeryRegularProcesses: 15
    celerySegmentProcesses: 15
    celeryRdbsig: 1

    workloadIdentity:
      projectID: bks-prd-workloads-1
      
    captcha:
      projectID: bks-prd-workloads-1

    kafka:
      brokerBootstrapServers: seed-312ca77d.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-8c152181.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:30081

      resourcesSecretManager:
        projectID: bks-kafka-prd-2-us-c1

  extraEnvs:
    - name: STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID
      value: stats-and-reports-prd
    - name: STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME
      value: stats-and-reports

  resources:
    api:
      limits:
        cpu: 4000m
        memory: 5Gi
      requests:
        cpu: 3000m
        memory: 3Gi
    celeryIndex:
      limits:
        cpu: 4000m
        memory: 8Gi
      requests:
        cpu: 3000m
        memory: 8Gi
    celeryPriority:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    celeryRegular:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    celerySegment:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    reportsApi:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 2000m
        memory: 4Gi
    searchApi:
      limits:
        cpu: 4000m
        memory: 5Gi
      requests:
        cpu: 3000m
        memory: 3Gi
    tools:
      limits:
        cpu: 4000m
        memory: 10Gi
      requests:
        cpu: 900m
        memory: 9Gi

  booksyWorkloadPriority:
    admin: p3
    api: p1
    celeryBeat: p1
    celeryEta: p1
    celeryIndex: p1
    celeryPriority: p1
    celeryPush: p1
    celeryRegular: p1
    celerySegment: p1
    grpcApi: p1
    publicApi: p3
    reportsApi: p2
    searchApi: p1
    pubsubWorkerNotificationsWebhooks: p2
    pubsubWorkerProviderCalendarImporter: p2
    tools: p5
    checkQueue: p5
    init: p1
    dbInit: p1
    elasticInit: p1
    executeScriptsInit: p1
    otherInit: p1
