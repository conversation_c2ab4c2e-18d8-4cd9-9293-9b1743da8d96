# pylint: disable=duplicate-code

from settings.es_countries.languages import (
    ENGLISH,
    SPANISH,
    SYNONYMS_FILES,
)


ES_LANGUAGES = [ENGLISH, SPANISH]
ES_SYNONYMS_FILE = SYNONYMS_FILES[ENGLISH]

ES_COMPLETER_DEFAULT_LAT_LON = "11.6736183,118.1265216"
ES_THRESHOLD_EXTRA_SCORE = 15

ES_REGION_LVL_4 = ['city', 'village', 'cdp']
ES_REGION_LVL_5 = ['zip']
ES_REGION_LVL_7 = []

ES_SUGGESTION_REGION_TYPES = [
    'zip',
    'city',
    'neighborhood',
    'metropolis',
]
ES_SEARCHABLE_REGION_TYPES = [
    'zip',
    'city',
    'neighborhood',
    'country',
    'metropolis',
    'village',
    'cdp',
]

ES_CITY_LVL = ES_REGION_LVL_4
ES_NEIGHBORHOOD_LVL = ES_REGION_LVL_7
ES_ZIP_LVL = ES_REGION_LVL_5

ES_REGION_TYPE_BOOST = {
    'country': 990,  # -10,
    'state': 0,  # -1000,
    'county': 0,  # -1000,
    'city': 1000,  # 0,
    'metropolis': 1000,  # 0,
    'town': 1000,  # 0,
    'village': 950,  # -50,
    'cdp': 950,  # -50,
    'neighborhood': 990,  # -10,
    'zip': 1000,  # 0
}
