from lib.test_utils import create_test_reciever, create_test_push_token
from webapps.notification.models import Reciever
from webapps.user.models import User, UserProfile


class PushReceiverHandlersTestMixin:
    def setUp(self):  # pylint: disable=invalid-name
        super().setUp()
        self.token = create_test_push_token(self.default_device)

    def _create_reciever(self, **kwargs):
        logged_in_user = User.objects.filter(
            id=self.logged_in_user_id,
        ).first()
        return create_test_reciever(logged_in_user.id, self.default_device, self.token, **kwargs)

    @property
    def default_device(self):
        if self._is_ios_test():
            return Reciever.IOS
        return Reciever.ANDROID

    @classmethod
    def _is_ios_test(cls):
        return 'ios' in cls.__module__.lower()


class BusinessPushReceiverHandlersTestMixin(PushReceiverHandlersTestMixin):
    def _create_reciever(self, **kwargs):
        logged_in_user = User.objects.filter(
            id=self.logged_in_user_id,
        ).first()
        kwargs['profile_type'] = UserProfile.Type.BUSINESS
        kwargs['business'] = kwargs.get('business', self.business)
        return create_test_reciever(
            logged_in_user.id,
            self.default_device,
            token=kwargs.pop('token', None) or self.token,
            **kwargs,
        )
