import logging

from bo_obs.datadog.enums import BooksyTeams

from service.account import (
    AccountRequestHandler,
)
from service.tools import (
    RequestHandler,
    json_request,
    session,
)
from webapps.user.serializers import (
    UserAgreementsGetSerializer,
    UserAgreementsUpdateSerializer,
)

logger = logging.getLogger('booksy.repeated_requests')


class UserAgreementsHandler(
    AccountRequestHandler, RequestHandler
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @session(optional_login=True)
    def get(self):
        """
        swagger:
            summary: Details about UserAgreements
            type: UserAgreements

        :swagger
        swaggerModels:
            UserAgreements:
                id: UserAgreements
                required:
                    - user_agreements
                properties:
                    user_agreements:
                        type: array
                        items:
                            type: PrivacyAgreement
        """
        serializer = UserAgreementsGetSerializer(instance=self.user)
        if self.user:
            result = serializer.data
        else:
            result = {'user_agreements': serializer.get_user_agreements(instance=None)}

        self.finish(result)

    @json_request
    @session(login_required=True)
    def put(self, *args, **kwargs):
        """
        swagger:
            summary: Update user agreements
            parameters:
                - name: body
                  paramType: body
                  type: UserAgreementRequest
                  description: UserAgreements
            type: UserAgreements

        :swagger
        swaggerModels:
            UserAgreementRequest:
                id: UserAgreementRequest
                required:
                    - user_agreements
                properties:
                    user_agreements:
                        type: array
                        items:
                            type: PrivacyAgreement
        """
        serializer = UserAgreementsUpdateSerializer(
            data=self.data, context={'user_id': self.user.id}
        )
        self.validate_serializer(serializer)
        serializer.save()
        # return updated values
        serializer = UserAgreementsGetSerializer(instance=self.user)

        self.finish(serializer.data)
