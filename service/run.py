import logging
import os

import django


django.setup()  # noqa

from django.conf import settings


import tornado.web
import tornado.wsgi
from tornado.log import enable_pretty_logging
from tornado.options import define, options
from lib.uwsgi_tools import BooksyWSGIApplication, BooksyWSGIAdapter

import service.account
import service.b2b_referral.b2b_referral
import service.billing.discount_code
import service.billing.invoices
import service.billing.payment
import service.billing.settings
import service.billing.stripe.payment_method
import service.billing.subscription
import service.billing.terms
import service.booking.appointments
import service.booking.appointments_family_and_friends
import service.booking.booking_actions
import service.booking.business_booking
import service.booking.customer_booking
import service.booking.repeating
import service.booking.wait_list
import service.booksy_auth.webhook
import service.boost.boost
import service.braintree_app.payment_methods
import service.business.service_addons
import service.business.account
import service.business.account_deletion
import service.business.agreements
import service.business.appsflyer
import service.business.attendance_list
import service.business.calendars
import service.business.category
import service.business.create
import service.business.customer_info
import service.business.invite_again
import service.business.customer_info_thin
import service.business.details
import service.business.feature_status.handlers
import service.business.locale
import service.business.locked
import service.business.monthly_bookings
import service.business.notification_list
import service.business.notifications
import service.business.opening_hours
import service.business.other
import service.business.renting_venue
import service.business.reviews
import service.business_related.safety_rules
import service.business.service_promotions
import service.business.service_suggestion
import service.business.service_usage
import service.business.services
import service.business.sms
import service.business.statistics
import service.business_related.amenities
import service.c2b_referral.get_c2b_data
import service.consents.consents
import service.customer.agreements
import service.customer.book_again
import service.customer.customers
import service.customer.my_booksy
import service.customer.notifications
import service.customer.pop_up_notification
import service.customer.reviews
import service.customer.timeslots
import service.dashboard
import service.df_creator.digital_flyers
import service.error
import service.experiment_v3.business_experiment
import service.experiment_v3.named_experiment
import service.facebook
import service.feedback.feedback
import service.images.handlers
import service.images.v2.handlers
import service.images.renting_venue
import service.intro_screen.handlers
import service.market_pay.account_holder
import service.marketing.analytics
import service.marketing.unsubscribe
import service.marketing.dynamic_listing
import service.marketplace.marketplace
import service.message_blast.images
import service.message_blast.message_blast
import service.metrics.business_application
import service.notification.webhook
import service.other.cloudfront
import service.other.apple_maps
import service.other.celery
import service.other.contact_us.handler
import service.other.debug
import service.other.docs
import service.other.feature_flags
import service.other.gdpr
import service.other.geocoding
import service.other.physical_therapy
import service.other.promoted_business_tile_slots
import service.other.push
import service.other.redirects
import service.other.server_time
import service.other.static_content
import service.other.utils
import service.other.warehouse
import service.other.wait_list
import service.other.website
import service.other.zoom
import service.partners.facebook.v3.handler
import service.partners.groupon.v1.availability
import service.partners.groupon.v1.booking
import service.partners.groupon.v1.handler
import webapps.printer_api.login
import webapps.printer_api.config
import service.printer_api.printout
import webapps.printer_api.printers
import service.photo
import service.pos.business_commissions
import service.pos.business_dashboard
import service.pos.business_pos
import service.pos.business_transactions
import service.pos.business_bsx
import service.pos.customer_transactions
import service.pos.payment_methods
import service.pos.registers
import service.purchase
import service.renting_venue.change_details
import service.resources
import service.siwa
import service.schedule.working_hours
import service.search.business
import service.search.region
import service.search.venue
import service.search.venue_street
import service.segment.counters
import service.stripe_integration.account
import service.stripe_integration.reader
import service.stripe_integration.checkout
import service.voucher.voucher
import service.voucher.voucher_customer
import service.voucher.orders_customer
import service.warehouse.warehouses
import service.warehouse.wholesalers
import service.warehouse.brands
import service.warehouse.commodities_archive
import service.warehouse.barcode
import service.warehouse.barcode_list
import service.warehouse.barcode_generator
import service.warehouse.commodity_category_details
import service.warehouse.commodity_category_list
import service.warehouse.commodities_delete
import service.warehouse.commodity_details
import service.warehouse.commodity_history
import service.warehouse.commodity_list
import service.warehouse.commodity_categories_tree
import service.warehouse.commodity_import
import service.warehouse.commodity_order
import service.warehouse.internal_expenditure_reason
import service.warehouse.volume_measure
import service.warehouse.supplier
import service.warehouse.document
import service.warehouse.stock_level_history
import service.warehouse.warehouse_commodities
import service.warehouse.photo
import service.warehouse.quick_order
import service.warehouse.formula
import service.sequencing_number.documents
import service.warehouse.commodity_consumption
import webapps.market_pay.views
import service.invoicing.invoice
import service.invoicing.buyers
import service.invoicing.cash_register_document
import service.invoicing.sellers


from service.tools import BaseBooksyRequestHandler
from settings.local import LIVE_DEPLOYMENT
import versions

import webapps.stats_and_reports.handlers


raw_url_handlers = [
    ############################################################
    ####################### CUSTOMER API #######################
    ############################################################
    # CUSTOMER NOTIFICATIONS
    (r"/customer_api/me/notifications/?", service.customer.notifications.NotificationsHandler),
    (
        r"/customer_api/me/notifications/ios/([^/]*)/?",
        service.customer.notifications.PushReceiverHandler,
    ),
    (
        r"/customer_api/me/notifications/android/([^/]*)/?",
        service.customer.notifications.AndroidPushReceiverHandler,
    ),
    (
        r"/customer_api/me/notifications/([^/]*)/([PEAI])/?",
        service.customer.notifications.NotificationsChangeHandler,
    ),
    (r"/customer_api/me/ios_notification_badges/?", service.other.push.NotificationBadgesHandler),
    (r"/customer_api/unsubscribe/?", service.account.UnsubscribeHandler),
    (r"/customer_api/blasts/unsubscribe/?", service.marketing.unsubscribe.UnsubscribeBlastsHandler),
    # CUSTOMER
    (r"/customer_api/account/exists/?", service.account.CheckAccountExistsCustomerHandler),
    (r"/customer_api/account/login/?", service.customer.customers.CustomerLoginHandler),
    (r"/customer_api/account/logout/?", service.account.LogoutCustomerHandler),
    (r"/customer_api/account/login/facebook/?", service.facebook.FacebookLoginCustomerHandler),
    (r"/customer_api/account/login/apple/?", service.siwa.AppleLoginCustomerHandler),
    (r"/customer_api/account/login/apple/v2/?", service.siwa.AppleLoginCustomerHandlerV2),
    (r"/customer_api/account/password_reset/?", service.account.AccountPasswordResetHandler),
    (
        r"/customer_api/account/password_change/?",
        service.account.AccountPasswordChangeCustomerHandler,
    ),
    (r"/customer_api/account/jwt_password_reset/?", service.account.JWTAccountPasswordResetHandler),
    (
        r"/customer_api/account/jwt_password_change/?",
        service.account.JWTAccountPasswordChangeHandler,
    ),
    (r"/customer_api/account/jwt_email_reset/?", service.account.JWTAccountEmailResetHandler),
    (r"/customer_api/account/jwt_email_change/?", service.account.JWTAccountEmailChangeHandler),
    (r"/customer_api/account/jwt_token_validation/?", service.account.JWTTokenValidationHandler),
    (r"/customer_api/account/?", service.customer.customers.CustomerCreateHandler),
    (r"/customer_api/account/sms_code/?", service.customer.customers.CustomerSMSCodeHandler),
    (r"/customer_api/sms_invite/?", service.customer.customers.CustomerSMSInviteHandler),
    (r"/customer_api/me/?", service.customer.customers.CustomerAccountHandler),
    (
        r"/customer_api/me/update_details/?",
        service.customer.customers.CustomerAccountHandler,
    ),  # TODO: deprecated
    (r"/customer_api/me/change_email/?", service.customer.customers.CustomerChangeEmailHandler),
    (r"/customer_api/me/home/<USER>", service.dashboard.HomepageHandler),
    (r"/customer_api/me/bookmarks/?", service.customer.customers.CustomerBookmarksListHandler),
    (r"/customer_api/me/bookmarks/(\d+)/?", service.customer.customers.CustomerBookmarksHandler),
    (
        r"/customer_api/me/resource_bookmarks/(\d+)/?",
        service.customer.customers.CustomerResourceBookmarksHandler,
    ),
    (r"/customer_api/me/likes/?", service.customer.customers.CustomerLikesHandler),
    (r"/customer_api/me/likes/bulk_create/?", service.customer.customers.LikesBulkCreateHandler),
    (r"/customer_api/me/connect/?", service.facebook.FacebookConnectHandler),
    (r"/customer_api/me/history/?", service.customer.customers.CustomerHistoryHandler),
    (r"/customer_api/me/photo/?", service.customer.customers.CustomerPhotoHandler),
    (r"/customer_api/my_booksy/?", service.customer.my_booksy.MyBooksy),
    (r"/customer_api/my_booksy/galleries/?", service.customer.my_booksy.MyBooksyGalleries),
    # region deprecated since August 2019 (task 50776)
    (r"/customer_api/my_booksy_top_rated/?", service.customer.my_booksy.MyBooksyTopRated),
    (
        r"/customer_api/my_booksy_welcome_categories/?",
        service.customer.my_booksy.MyBooksyWelcomeCategories,
    ),
    # endregion deprecated since August 2019 (task 50776)
    # CUSTOMER POP-UP NOTIFICATIONS
    (
        r"/customer_api/pop_up_notification/"
        r"(?P<notification_id>\d+)/(?P<action_type>rejected|used)/action/?",
        service.customer.pop_up_notification.PopUpNotificationAction,
    ),
    # APPOINTMENTS
    (
        r"/customer_api/me/appointments/business/(?P<business_id>\d+)/?",
        service.booking.appointments.CreateCustomerAppointmentHandler,
        {'dry_run': False},
    ),
    (
        r"/customer_api/me/appointments/business/(?P<business_id>\d+)/dry_run/?",
        service.booking.appointments.CreateCustomerAppointmentHandler,
        {'dry_run': True},
    ),
    # old: backward compatibility
    (
        r"/customer_api/me/appointments/"
        r"(?P<appointment_type>single|multi)/(?P<appointment_id>\d+)/?",
        service.booking.appointments.OldCustomerAppointmentHandler,
    ),
    # new
    (
        r"/customer_api/me/appointments/(?P<appointment_uid>\d+)/?",
        service.booking.appointments.CustomerAppointmentHandler,
        {'dry_run': False},
    ),
    (
        r"/customer_api/me/appointments/(?P<appointment_uid>\d+)/dry_run/?",
        service.booking.appointments.CustomerAppointmentHandler,
        {'dry_run': True},
    ),
    # old: backward compatibility
    (
        r"/customer_api/me/appointments/"
        r"(?P<appointment_type>single|multi)/(?P<appointment_id>\d+)/action/?",
        service.booking.booking_actions.OldCustomerAppointmentActionsHandler,
    ),
    (
        r"/customer_api/me/appointments/(?P<appointment_uid>\d+)/action/?",
        service.booking.booking_actions.CustomerAppointmentActionsHandler,
    ),
    # DEPREACTED - Use /book_again_v2 DRF endpoint instead
    (
        r"/customer_api/me/book_again/?",
        service.customer.book_again.BookAgainHandler,
    ),  # Book again should always be in dry_run mode
    # DEPRECATED - book again is always called in dry_run mode. Use /book_again_v2 DRF endpoint instead
    (
        r"/customer_api/me/book_again/dry_run/?",
        service.customer.book_again.BookAgainHandler,
    ),
    # new
    (r"/customer_api/me/appointments/wait_list/?", service.booking.wait_list.WaitListHandler),
    (
        r"/customer_api/me/appointments/(?P<appointment_uid>\d+)/claim/?",
        service.booking.appointments.CustomerAppointmentClaimHandler,
    ),
    (
        r"/customer_api/me/businesses/(\d+)/services/(\d+)/addons/?",
        service.business.service_addons.CustomerServiceAddOnsListHandler,
    ),
    (r"/customer_api/me/services/services_price/?", service.business.services.ServicesPriceHandler),
    # DEPRECATED BOOKING HANDLERS
    (r"/customer_api/me/bookings/?", service.booking.customer_booking.CustomerBookingHandler),
    # TIME SLOTS
    (r"/customer_api/me/bookings/time_slots/?", service.customer.timeslots.TimeSlotsHandler),
    (
        r"/customer_api/me/bookings/(\d+)/time_slots/?",
        service.customer.timeslots.BookingTimeSlotsHandler,
    ),
    (
        r"/customer_api/me/businesses/(\d+)/appointments/time_slots/?",
        service.customer.timeslots.AppointmentTimeSlotsHandler,
    ),
    (
        r"/customer_api/me/businesses/(\d+)/appointments/(\d+)/time_slots/?",
        service.customer.timeslots.AppointmentEditTimeSlotsHandler,
    ),
    # BUSINESS
    (r"/customer_api/businesses/?", service.search.business.BusinessSearchHandler),
    (r"/customer_api/businesses/gallery/?", service.customer.my_booksy.BusinessesGalleryHandler),
    (
        r"/customer_api/businesses/faceting_category/?",
        service.search.business.BusinessCategoryFacetingHandler,
    ),
    (
        r"/customer_api/businesses/faceting_region/?",
        service.search.business.BusinessRegionFacetingHandler,
    ),
    (
        r"/customer_api/businesses/faceting_treatment/category/(\d+)/?",
        service.search.business.BusinessTreatmentFacetingHandler,
    ),
    (r"/customer_api/businesses/cms_content/?", service.marketplace.marketplace.CmsContentHandler),
    (r"/customer_api/businesses/(\d+)/?", service.business.details.BusinessDetailsHandler),
    (
        r"/customer_api/businesses/fb_page_app/([0-9a-zA-Z]+)/?",
        service.search.business.BusinessIDByFacebookPageIDHandler,
    ),
    # ANALYTICS
    (
        r"/customer_api/trigger_event/cb_started_for_customer/?",
        service.marketing.analytics.AnalyticsCBStartedForCustomerHandler,
    ),
    # Feedback
    (r"/customer_api/feedback/?", service.feedback.feedback.FeedbackHandler),
    (
        r"/customer_api/change_venue_details_request/?",
        service.renting_venue.change_details.ChangeDetailsVenueHandler,
    ),
    # BUSINESS REVIEWS
    (r"/customer_api/me/reviews/?", service.customer.reviews.CustomerReviewsHandler),
    (
        r"/customer_api/me/reviews/awaiting/?",
        service.customer.reviews.CustomerReviewsAwaitingHandler,
    ),
    (
        r"/customer_api/businesses/(\d+)/reviews/?",
        service.customer.reviews.CustomerReviewsPerBusinessHandler,
    ),
    (
        r"/customer_api/businesses/(\d+)/reviews/(\d+)/?",
        service.customer.reviews.CustomerSpecificReviewHandler,
    ),
    (
        r"/customer_api/businesses/(\d+)/reviews/(\d+)/feedback/?",
        service.customer.reviews.CustomerSpecificReviewFeedbackHandler,
    ),
    (
        r"/customer_api/businesses/(\d+)/reviews/(\d+)/photos/?",
        service.customer.reviews.CustomerSpecificReviewPhotosHandler,
    ),
    (
        r"/customer_api/businesses/(\d+)/reviews/(\d+)/photos/(\d+)/?",
        service.customer.reviews.CustomerSpecificReviewPhotoHandler,
    ),
    (r"/customer_api/customer/(\d+)/reviews/?", service.customer.reviews.CustomersReviewsHandler),
    # VARIOUS
    (r"/customer_api/search/location_hints/?", service.search.region.CustomerLocationHintsHandler),
    (r"/customer_api/search/street_hints/?", service.search.region.StreetHintsHandler),
    (r"/customer_api/search/street_hints/resolve/?", service.search.region.ResolveStreetHandler),
    (r"/customer_api/location/info/?", service.search.region.RegionInfoHandler),
    (
        r"/customer_api/gdpr_export_descriptions/?",
        service.other.gdpr.CustomerGDPRDescriptionHandler,
    ),
    # MP promoted business tiles
    (
        r"/customer_api/promoted_business_tile_slots/?",
        service.other.promoted_business_tile_slots.PromotedBusinessTileSlotHandler,
    ),
    # B_LISTINGS
    (r'/customer_api/b_listing/(\d+)/invite/?', service.marketplace.marketplace.InviteBListing),
    (r'/customer_api/b_listing/(\d+)/claim/?', service.marketplace.marketplace.ClaimBListing),
    # POS
    (
        r"/customer_api/me/transactions/?",
        service.pos.customer_transactions.CustomerTransactionsHandler,
    ),
    (
        r"/customer_api/me/transactions/(\d+)/?",
        service.pos.customer_transactions.CustomerTransactionDetailsHandler,
    ),
    (
        r"/customer_api/me/transactions/(\d+)/action/?",
        service.pos.customer_transactions.CustomerTransactionActionHandler,
    ),
    (
        r"/customer_api/me/transactions/(\d+)/send_receipt/?",
        service.pos.customer_transactions.CustomerTransactionSendReceiptHandler,
    ),
    (
        r"/customer_api/me/transactions/(\d+)/last_receipt/?",
        service.pos.customer_transactions.CustomerTransactionLastReceiptHandler,
    ),
    (
        r"/customer_api/me/payment_methods/?",
        service.pos.payment_methods.CustomerPaymentMethodsHandler,
    ),
    (
        r"/customer_api/me/payment_methods/(\d+)/?",
        service.pos.payment_methods.CustomerPaymentMethodDetailsHandler,
    ),
    # AGREEMENTS GDPR
    (r"/customer_api/agreements/?", service.customer.agreements.UserAgreementsHandler),
    # CONSENTS
    (r"/customer_api/me/consents/?", service.consents.consents.CustomerConsentsHandler),
    (r"/customer_api/me/consents/([-\w]+)/?", service.consents.consents.CustomerConsentHandler),
    (
        r"/customer_api/me/consents/([-\w]+)/sign/?",
        service.consents.consents.CustomerConsentSignHandler,
    ),
    (
        r"/customer_api/me/consents/([-\w]+)/signature/?",
        service.consents.consents.CustomerConsentSignatureHandler,
    ),
    (
        r"/customer_api/me/consents/([-\w]+)/photo/?",
        service.consents.consents.CustomerConsentPhotoHandler,
    ),
    (
        r"/customer_api/me/consents/([-\w]+)/pdf/?",
        service.consents.consents.CustomerConsentPdfHandler,
    ),
    (
        r"/customer_api/me/consents/([-\w]+)/send_email/?",
        service.consents.consents.CustomerConsentSendEmailHandler,
    ),
    # GEO DATA
    (r"/customer_api/reverse_geocode/?", service.other.geocoding.ReverseGeoDecoderFullHandler),
    (
        r"/customer_api/reverse_geocode/country/?",
        service.other.geocoding.ReverseGeoDecoderCountryHandler,
    ),
    (r"/customer_api/reverse_geocode/region/?", service.other.geocoding.ReverseGeoRegionHandler),
    # MARKETPLACE
    (r"/customer_api/business_network/([-\w]+)/?", service.other.website.BusinessNetworkHandler),
    (
        r"/customer_api/business_network/business/(?P<business_id>\d+)/?",
        service.marketplace.marketplace.GetBusinessNetworkHandler,
    ),
    # REFERRAL C2B
    (r"/customer_api/me/referral/?", service.c2b_referral.get_c2b_data.GetC2BDataHandler),
    # VOUCHERS
    (
        r"/customer_api/me/businesses/(?P<business_id>\d+)/voucher_order/?",
        service.voucher.orders_customer.CustomerVoucherOrderHandler,
    ),
    (
        r"/customer_api/me/businesses/(?P<business_id>\d+)/voucher_templates/?",
        service.voucher.orders_customer.CustomerVoucherTemplateListingHandler,
    ),
    (
        r"/customer_api/me/businesses/(?P<business_id>\d+)/voucher_templates/(?P<template_id>\d+)/?",
        service.voucher.orders_customer.CustomerVoucherTemplateDetailsHandler,
    ),
    (r"/customer_api/me/vouchers/?", service.voucher.voucher_customer.CustomerVoucherListHandler),
    (
        r"/customer_api/me/vouchers/(\d+)/?",
        service.voucher.voucher_customer.CustomerVoucherDetailsHandler,
    ),
    (
        r"/customer_api/me/claim_voucher/(\w+)/?",
        service.voucher.voucher_customer.ClaimVoucherHandler,
    ),
    # Safety Rules
    (
        r"/customer_api/businesses/(?P<business_id>\d+)/safety_rules/?",
        service.business_related.safety_rules.CustomerSafetyRulesHandler,
    ),
    # MARKETING
    (
        r"/customer_api/businesses/dynamic_listing/(?P<listing_slug>[-\w]+)/?",
        service.marketing.dynamic_listing.DynamicListingHandler,
    ),
    # FAMILY AND FRIENDS APPOINTMENTS
    (
        r"/customer_api/me/family_and_friends/members/(\d+)/appointments/?",
        service.booking.appointments_family_and_friends.MemberAppointmentsHandler,
    ),
    (
        r"/customer_api/me/family_and_friends/members/appointments/?",
        service.booking.appointments_family_and_friends.AppointmentsHandler,
    ),
    ############################################################
    ####################### BUSINESS API #######################
    ############################################################
    # BUSINESS
    (r"/business_api/businesses/(\d+)/?", service.business.details.BusinessBasicDetailsHandler),
    (r"/business_api/me/businesses/?", service.business.create.MyBusinessesHandler),
    (r"/business_api/me/businesses/(\d+)/?", service.business.create.MyBusinessHandler),
    (
        r"/business_api/me/businesses/(\d+)/account_deletion/?",
        service.business.account_deletion.MyBusinessAccountDeletionHandler,
    ),
    (
        r"/business_api/me/businesses/b_listing/?",
        service.business.create.MyBusinessBListingListHandler,
    ),
    (
        r"/business_api/me/businesses/b_listing/(?P<b_listing_id>\d+)/claim/?",
        service.business.create.MyBusinessBListingHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/activation_status/?",
        service.business.create.BusinessActivationStatusHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/change_package/(?P<business_package>[-\w]+)/?",
        service.business.create.MyBusinessPackageHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/activate/?",
        service.business.create.BusinessActivationHandler,
    ),
    (
        r"/business_api/me/sequencing_number/(\d+)/records/?",
        service.sequencing_number.documents.SequenceRecordsListHandler,
    ),
    (
        r"/business_api/me/sequencing_number/(\d+)/record/(\d+)/?",
        service.sequencing_number.documents.SequenceRecordHandler,
    ),
    (
        r"/business_api/me/sequencing_number/(\d+)/records_settings/?",
        service.sequencing_number.documents.SequenceRecordsSettingsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/service_categories/?",
        service.business.services.ServiceCategoriesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/service_categories/(\d+)/?",
        service.business.services.ServiceCategoryHandler,
    ),
    (r"/business_api/me/businesses/(\d+)/services/?", service.business.services.ServicesHandler),
    (
        r"/business_api/me/businesses/(\d+)/services/services_price/?",
        service.business.services.BusinessServicesPriceHandler,
    ),
    # BUSINESS ADDONS
    (
        r"/business_api/me/businesses/(\d+)/services/addons/?",
        service.business.service_addons.ServicesAddOnsListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/addons/(\d+)/?",
        service.business.service_addons.ServicesAddOnsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/(\d+)/addons/?",
        service.business.service_addons.ServiceAddOnsListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/addons/photo/?",
        service.business.service_addons.ServiceAddOnPhotosHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/addons/(\d+)/photo/?",
        service.business.service_addons.ServiceAddOnPhotoHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/suggest/?",
        service.business.services.ServicesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/(\d+)/?",
        service.business.services.ServiceHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/(\d+)/photo/?",
        service.photo.ServicePhotoListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/(\d+)/photo/(\d+)/?",
        service.photo.ServicePhotoDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/services/(\d+)/usage/?",
        service.business.service_usage.ServiceUsageInfoHandler,
    ),
    (r"/business_api/me/resources/(\d+)/time_offs/?", service.resources.ResourceTimeOffHandler),
    (r"/business_api/me/businesses/(\d+)/resources/?", service.resources.ResourceRichHandler),
    (r"/business_api/me/resources/(\d+)/?", service.resources.ResourceHandler),
    (r"/business_api/me/resources/photo/?", service.photo.ResourceUploadPhotoHandler),
    (r"/business_api/me/resources/(\d+)/photo/?", service.resources.ResourcePhotoHandler),
    (r"/business_api/me/resources/(\d+)/invite/?", service.resources.ResourceInviteHandler),
    (
        r"/business_api/me/businesses/(\d+)/service_hints/?",
        service.business.service_suggestion.ServiceHintsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/service_suggestion/?",
        service.business.service_suggestion.ServiceSuggestionHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/digital_flyer/?",
        service.df_creator.digital_flyers.DigitalFlyerCategoriesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/digital_flyer/hashtags/?",
        service.df_creator.digital_flyers.DigitalFlyerHashtagsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/digital_flyer/shared/?",
        service.df_creator.digital_flyers.DigitalFlyerSharedHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/digital_flyer/flyer_data/?",
        service.df_creator.digital_flyers.DigitalFlyerDataHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/digital_flyer/render/?",
        service.df_creator.digital_flyers.DigitalFlyerRenderHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/digital_flyer/flyer_data/?",
        service.df_creator.digital_flyers.DigitalFlyerDataHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/digital_flyer/(\w+)/?",
        service.df_creator.digital_flyers.DigitalFlyerCategoryHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/features_status/?",
        service.business.feature_status.handlers.BusinessFeatureStatusHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)"
        r"/features_status/service_promotions/?",
        service.business.feature_status.handlers.ServicePromotionsDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/features_status/boost/?",
        service.business.feature_status.handlers.BusinessBoostStatusHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/renting_venue/?",
        service.business.renting_venue.RentingVenueHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/renting_venue/cover_photo/?",
        service.images.renting_venue.VenueCoverPhotoHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/renting_venue/contractors/?",
        service.business.renting_venue.VenueContractorsListingHandler,
    ),
    # DEPRECATED
    (
        r"/business_api/me/businesses/(\d+)/service_wordclouds/?",
        service.business.service_suggestion.ServicesWordcloudsHandler,
    ),
    # DEPRECATED
    (
        r"/business_api/me/businesses/(\d+)/suggest_services/?",
        service.business.service_suggestion.OldServiceSuggestionHandler,
    ),
    # BUSINESS AGREEMENTS GDPR
    (
        r"/business_api/me/businesses/business_agreements/?",
        service.business.agreements.BusinessAgreementListing,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/agreements/?",
        service.business.agreements.BusinessAgreements,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/gdpr_annex/?",
        service.business.agreements.GDPRAnnexPdfPreviewHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/inspector/?",
        service.business.agreements.BusinessInspectorHandler,
    ),
    (
        r"/business_api/me/businesses/customer_agreements/?",
        service.business.agreements.BusinessCustomerAgreements,
    ),
    # BUSINESS IMAGES
    # Previous "v1" endpoints utilized Elastic Search to serve images, which imposed unwanted delays
    # from business perspective. We introduce "v2" endpoints which provide the same functionality
    # but serve responses from Postgres directly.
    (r"/business_api/me/businesses/(\d+)/images/v2/?", service.images.v2.handlers.ImageListHandler),
    (
        r"/business_api/me/businesses/(\d+)/images/v2/cover/?",
        service.images.v2.handlers.ImageCoverHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/images/v2/(\d+)/?",
        service.images.v2.handlers.ImageDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/images/v2/(\d+)/set_cover/?",
        service.images.v2.handlers.BusinessSetCoverImageHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/images/v2/(\d+)/move_after/?",
        service.images.v2.handlers.BusinessImageMoveAfterV2Handler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/images/v2/(\d+)/move_before/?",
        service.images.v2.handlers.BusinessImageMoveBeforeV2Handler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/images/v2/(\d+)/comments/?",
        service.images.v2.handlers.ImageCommentsListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/images/v2/(\d+)/comments/(\d+)/?",
        service.images.v2.handlers.ImageCommentsDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/image/publish/?",
        service.images.handlers.PublishPhotoToPortfolioHandler,
    ),
    (r"/business_api/me/businesses/(\d+)/images/?", service.images.handlers.ImageSearchHandler),
    (r"/business_api/images_like/(\d+)/?", service.images.handlers.ImageLikeHandler),
    (r"/business_api/images_comment/(\d+)/?", service.images.handlers.ImageCommentHandler),
    (r"/customer_api/images/?", service.images.handlers.ImageSearchHandler),
    (r"/customer_api/images/(\d+)/?", service.images.handlers.ImageSearchHandler),
    (r"/customer_api/images_like/(\d+)/?", service.images.handlers.ImageLikeHandler),
    (r"/customer_api/images_comment/(\d+)/?", service.images.handlers.ImageCommentHandler),
    # BUSINESS POP-UPS
    (
        r"/business_api/businesses/(\d+)/pop_up/?",
        service.business.notifications.BusinessPopupHandler,
    ),
    (
        r"/business_api/pop_up/"
        r"(?P<notification_id>\d+)/(?P<action_type>rejected|used)/action/?",
        service.customer.pop_up_notification.PopUpNotificationAction,
    ),
    # BUSINESS CUSTOMER INFO
    (
        r"/business_api/me/businesses/(\d+)/customers/?",
        service.business.customer_info.BusinessCustomerInfoListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/invite_again/?",
        service.business.invite_again.InviteAgainBusinessCustomerInfoListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/thin/?",
        service.business.customer_info_thin.BusinessCustomerInfoThinListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/?",
        service.business.customer_info.BusinessCustomerInfoHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/bookings/?",
        service.business.customer_info.BusinessCustomerInfoBookings,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/products/?",
        service.business.customer_info.BusinessCustomerInfoProductsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/photo/?",
        service.business.customer_info.BusinessCustomerPhotosHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/more_photos/?",
        service.business.customer_info.BusinessCustomerMorePhotosHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/tags/?",
        service.business.customer_info.BusinessCustomerInfoListTagsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customer/exists/?",
        service.business.customer_info.BusinessCustomerInfoExistsHandler,
    ),
    # BCI attached files
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/attached_files/?",
        service.business.customer_info.BusinessCustomerAttachedFilesListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/attached_files/(\d+)/?",
        service.business.customer_info.BusinessCustomerAttachedFilesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/attached_files/(\d+)/download/?",
        service.business.customer_info.BusinessCustomerAttachedFilesDownloadHandler,
    ),
    # BCI attached files S3
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/files/(\d+)/?",
        service.business.customer_info.BusinessCustomerAttachedFilesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/manual_merge/?",
        service.business.customer_info.ManualMergeBCIsHandler,
        {'dry_run': False},
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/manual_merge/dry_run/?",
        service.business.customer_info.ManualMergeBCIsHandler,
        {'dry_run': True},
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/merge/?",
        service.business.customer_info.GetBCIMergePossibilitiesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/locked/?",
        service.business.customer_info.BusinessCustomerLockedHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/import/?",
        service.business.customer_info.BusinessCustomerImportHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/import/json/?",
        service.business.customer_info.BusinessCustomerImportHandlerJSON,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/import/last_import/?",
        service.business.customer_info.BusinessCustomerLastImportHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/fizjo/bookings/?",
        service.business.customer_info.BookingsInfoForFizjoHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/patient_file_fields_with_options/?",
        service.other.physical_therapy.PatientFileFieldsEmptyHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/patient_file_fields_with_options/(\d+)/?",
        service.other.physical_therapy.PatientFileFieldsHandler,
    ),
    # APPOINTMENTS (wrapped single/multi bookings)
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/?",
        service.booking.appointments.CreateBusinessAppointmentHandler,
        {'dry_run': False},
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/dry_run/?",
        service.booking.appointments.CreateBusinessAppointmentHandler,
        {'dry_run': True},
    ),
    # old: backward compatibility
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/"
        r"(?P<appointment_type>single|multi)/(?P<appointment_id>\d+)/?",
        service.booking.appointments.OldBusinessAppointmentHandler,
    ),
    # new
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/"
        r"(?P<appointment_uid>\d+)/?",
        service.booking.appointments.BusinessAppointmentHandler,
        {'dry_run': False},
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/"
        r"(?P<appointment_uid>\d+)/dry_run/?",
        service.booking.appointments.BusinessAppointmentHandler,
        {'dry_run': True},
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/"
        r"(?P<appointment_uid>\d+)/book_again/?",
        service.booking.appointments.BookAgainBusinessAppointmentHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/"
        r"(?P<appointment_uid>\d+)/action/?",
        service.booking.booking_actions.BusinessAppointmentActionsHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/"
        r"(?P<appointment_uid>\d+)/notify_ready/?",
        service.booking.booking_actions.BusinessAppointmentNotifyReadyHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/appointments/"
        r"(?P<appointment_type>single|multi)/(?P<appointment_id>\d+)/notify_ready/?",  # pylint: disable=line-too-long
        service.booking.booking_actions.OldBusinessAppointmentNotifyReadyHandler,
    ),
    # BUSINESS BOOKING
    (r"/business_api/me/bookings/(\d+)/?", service.booking.business_booking.BusinessBookingHandler),
    # REPEATING BOOKING
    (
        r"/business_api/me/businesses/(\d+)/repeating_bookings/(\d+)/?",
        service.booking.repeating.BusinessRepeatingBookingHandler,
    ),
    # DASHBOARD
    (
        r"/business_api/me/bookings/(\d+)/archive/?",
        service.booking.business_booking.BusinessBookingArchiveHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/booking_notifications/?",
        service.dashboard.BusinessBookingsNotificationsHandler,
    ),
    # SMS HANDLERS
    (
        r"/business_api/me/businesses/(\d+)/sms/management/?",
        service.business.sms.BusinessSMSManagementHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/sms/history/?",
        service.business.sms.BusinessSMSHistoryHandler,
    ),
    # LOCKED HANDLERS
    (
        r"/business_api/me/businesses/(\d+)/locked/settings/?",
        service.business.locked.BusinessLockedSettingsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/locked/history/?",
        service.business.locked.BusinessLockedHistoryHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/locked/summary/?",
        service.business.locked.BusinessLockedSummaryHandler,
    ),
    # LEGACY STATISTICS (Booksy 2.0)
    (
        r"/business_api/me/businesses/(\d+)/business_statistics/?",
        service.business.statistics.BusinessStatisticsHandler,
    ),
    # BUSINESS NEW STATISTICS
    (
        r"/business_api/me/businesses/(\d+)/stats/report/send_by_email/?",
        webapps.stats_and_reports.handlers.ReportsSendByEmailRedirectHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stats/simple_statistics/tiles/?",
        webapps.stats_and_reports.handlers.SimpleStatisticTilesListRedirectHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stats/simple_statistics/tiles/(\w+)/?",
        webapps.stats_and_reports.handlers.SimpleStatisticTileDetailsRedirectHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stats/simple_statistics/commission_stats/?",
        webapps.stats_and_reports.handlers.SimpleCommissionStatsRedirectHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stats/commission_stats/?",
        webapps.stats_and_reports.handlers.CommissionStatsRedirectHandler,
    ),
    # BUSINESS NEW STATISTICS ipv6 fixes
    (
        r"/business_api/me/stats/businesses/(\d+)/report/?",
        webapps.stats_and_reports.handlers.ReportsHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/report/download/?",
        webapps.stats_and_reports.handlers.ReportsDownloadHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/report/send_by_email/?",
        webapps.stats_and_reports.handlers.ReportsSendByEmailHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/section/?",
        webapps.stats_and_reports.handlers.ReportSectionHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/dashboard/?",
        webapps.stats_and_reports.handlers.ReportsDashboardHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/dashboard/main/?",
        webapps.stats_and_reports.handlers.ReportsMainDashboardHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/simple_statistics/?",
        webapps.stats_and_reports.handlers.SimpleStatisticHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/simple_statistics/tiles/?",
        webapps.stats_and_reports.handlers.SimpleStatisticTilesListHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/simple_statistics/tiles/(\w+)/?",
        webapps.stats_and_reports.handlers.SimpleStatisticTileDetailsHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/simple_statistics/commission_stats/?",
        webapps.stats_and_reports.handlers.SimpleCommissionStatsHandler,
    ),
    (
        r"/business_api/me/stats/businesses/(\d+)/commission_stats/?",
        webapps.stats_and_reports.handlers.CommissionStatsHandler,
    ),
    # BUSINESS RESERVATIONS
    (
        r"/business_api/me/businesses/(\d+)/reservations/?",
        service.booking.business_booking.BusinessCreateReservationsHandler,
    ),
    (
        r"/business_api/me/reservations/(\d+)/?",
        service.booking.business_booking.BusinessReservationsHandler,
    ),
    # VARIOUS
    (r"/business_api/me/businesses/(\d+)/calendar/?", service.business.calendars.CalendarHandler),
    (
        r"/business_api/me/businesses/(\d+)/monthly_bookings/?",
        service.business.monthly_bookings.MonthlyBookingsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/boh_to_rwo/?",
        service.business.opening_hours.OpeningHoursHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/calendar/drag_subbooking/(?P<booking_id>\d+)/?",
        service.booking.appointments.CalendarDragSubbookingHandler,
    ),
    (r"/business_api/categories/?", service.business.category.CategoriesHandler),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/categories/?",
        service.business.category.BusinessCategoriesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/fb_page_app/?",
        service.business.create.BusinessFBPageAppHandler,
    ),
    (r"/business_api/me/businesses/(\d+)/locale/?", service.business.locale.BusinessLocaleHandler),
    (
        r"/business_api/me/businesses/(\d+)/gdpr_export_descriptions/?",
        service.other.gdpr.BusinessGDPRDescriptionHandler,
    ),
    # ACCOUNT and PROFILE
    (r"/business_api/account/?", service.business.account.BusinessAccountCreateHandler),
    (r"/business_api/account/exists/?", service.account.CheckAccountExistsBusinessHandler),
    (r"/business_api/account/login/?", service.business.account.BusinessAccountLoginHandler),
    (r"/business_api/account/login/facebook/?", service.facebook.FacebookLoginBusinessHandler),
    (r"/business_api/account/login/apple/?", service.siwa.AppleLoginBusinessHandler),
    (
        r"/business_api/account/login/auth_token/?",
        service.business.account.BusinessAccountAuthTokenLoginHandler,
    ),
    (r"/business_api/account/logout/?", service.account.LogoutBusinessHandler),
    (r"/business_api/account/password_reset/?", service.account.AccountPasswordResetHandler),
    (
        r"/business_api/account/password_change/?",
        service.account.AccountPasswordChangeBusinessHandler,
    ),
    (r"/business_api/account/jwt_password_reset/?", service.account.JWTAccountPasswordResetHandler),
    (
        r"/business_api/account/jwt_password_change/?",
        service.account.JWTAccountPasswordChangeHandler,
    ),
    (r"/business_api/account/jwt_email_reset/?", service.account.JWTAccountEmailResetHandler),
    (r"/business_api/account/jwt_email_change/?", service.account.JWTAccountEmailChangeHandler),
    (r"/business_api/account/jwt_token_validation/?", service.account.JWTTokenValidationHandler),
    (r"/business_api/me/?", service.business.account.BusinessAccountHandler),
    (r"/business_api/me/active/?", service.business.account.BusinessAccountSessionStatusHandler),
    (
        r"/business_api/me/auth_token/?",
        service.business.account.BusinessAccountSessionAuthTokenHandler,
    ),
    (r"/business_api/me/connect/?", service.facebook.FacebookConnectHandler),
    # VARIOUS
    (r"/business_api/region_hints/?", service.search.region.BusinessRegionHintsHandler),
    (r"/business_api/gdpr_info/?", service.other.gdpr.BusinessGDPRInfoHandler),
    (r"/business_api/gdpr_annex_sample/?", service.other.gdpr.BusinessGDPRAnnexHandler),
    # NOTIFICATIONS
    (
        r"/business_api/me/businesses/(\d+)/notifications/?",
        service.business.notifications.BusinessNotificationsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/notifications/([^/]*)/?",
        service.business.notifications.BusinessReceiverGetHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/notifications/ios/([^/]*)/?",
        service.business.notifications.BusinessPushReceiverHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/notifications/android/([^/]*)/?",
        service.business.notifications.BusinessAndroidPushReceiverHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/notifications/email/" r"([^/]*)/([^/]*)/check/?",
        service.business.notifications.BusinessEmailReceiverCheckPushHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/notifications/email/([^/]*)/?",
        service.business.notifications.BusinessEmailReceiverHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/notifications/([^/]*)/([PEAI])/?",
        service.business.notifications.BusinessNotificationsChangeHandler,
    ),
    (r"/business_api/me/ios_notification_badges/?", service.other.push.NotificationBadgesHandler),
    (r"/business_api/unsubscribe/?", service.account.UnsubscribeHandler),
    (
        r"/business_api/me/businesses/(\d+)/ask_pro_version/?",
        service.business.notifications.RequestSwitchToProHandler,
    ),
    # BOOKSY 3.0 NOTIFICATIONS
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/notification_list/(?P<status>new|active|archived)/?",
        service.business.notification_list.BusinessNotificationListHandler,
    ),
    # REVIEWS
    (
        r"/business_api/me/businesses/(\d+)/reviews/?",
        service.business.reviews.BusinessReviewsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/reviews/(\d+)/?",
        service.business.reviews.BusinessReviewSingleHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/reviews/(\d+)/reply/?",
        service.business.reviews.BusinessReviewReplyHandler,
    ),
    # POS
    (r"/business_api/me/businesses/(\d+)/pos/?", service.pos.business_pos.BusinessPOSHandler),
    (
        r"/business_api/me/businesses/(\d+)/pos/apply_tax_rate/(\w+)/?",
        service.pos.business_pos.ApplyDefaultTaxRateHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/business_cash_flow/?",
        service.pos.business_transactions.BusinessCashFlowHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/payment_rows/?",
        service.pos.business_transactions.PaymentRowListingHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/payment_rows_summary/?",
        service.pos.business_transactions.PaymentRowsSummaryHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/?",
        service.pos.business_transactions.BusinessTransactionsHandler,
        {'dry_run': False},
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/dry_run/?",
        service.pos.business_transactions.BusinessTransactionsHandler,
        {'dry_run': True},
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/?",
        service.pos.business_transactions.BusinessTransactionDetailsHandler,
        {'dry_run': False},
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/dry_run/?",
        service.pos.business_transactions.BusinessTransactionDetailsHandler,
        {'dry_run': True},
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/action/?",
        service.pos.business_transactions.BusinessTransactionActionHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/print_receipt/?",
        service.printer_api.printout.PrintoutHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/send_receipt/?",
        service.pos.business_transactions.BusinessTransactionSendReceiptHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/last_receipt/?",
        service.pos.business_transactions.BusinessTransactionLastReceiptHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/create_invoice/?",
        service.pos.business_transactions.BusinessTransactionCreateInvoiceHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/series/?",
        service.pos.business_transactions.BusinessTransactionSeriesDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/payment_row/(\d+)/request_refund/?",
        service.pos.business_transactions.BusinessRequestRefund,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/booking_charges/?",
        service.pos.business_dashboard.BusinessBookingsChargesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/pay_by_app_info/?",
        service.pos.business_pos.BusinessPayByAppInfoPlanHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/pos_plan_info/?",
        service.pos.business_pos.BusinessPosPlanInfoHandler,
    ),
    # POS: BSX RECEIPTS
    (
        r"/business_api/me/businesses/(\d+)/pos/bsx_file_link/?",
        service.pos.business_bsx.BsxFileHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/bsx/?",
        service.pos.business_bsx.BsxReceiptHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/transactions/(\d+)/bsx/log/?",
        service.pos.business_bsx.BsxLogHandler,
    ),
    # NO_SHOW_PROTECTION Payments
    (
        r"/business_api/me/businesses/(\d+)/pos/no_show_fee_service_protection/?",
        service.pos.business_pos.NoShowProtectionFeeServiceHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/no_show_fee_protection_all_services/?",
        service.pos.business_pos.NoShowProtectionFeeAllServicesHandler,
    ),
    # Voucher
    (r"/business_api/me/businesses/(\d+)/vouchers/?", service.voucher.voucher.VoucherHandler),
    (
        r"/business_api/me/businesses/(\d+)/vouchers/(\d+)/?",
        service.voucher.voucher.VoucherDetailHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/vouchers_aggregate/(?P<voucher_type>M|P)/(?P<customer_card_id>\d+)/?",
        service.voucher.voucher.VoucherAggregateHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/voucher_templates/?",
        service.voucher.voucher.VoucherTemplateHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/voucher_templates/(\d+)/?",
        service.voucher.voucher.VoucherTemplateDetailHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/search_items/?",
        service.pos.business_pos.BusinessItemsSearchHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/?",
        service.pos.business_commissions.CommissionDefaultsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/resource/(\d+)/?",
        service.pos.business_commissions.CommissionDefaultsStafferHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/products/?",
        service.pos.business_commissions.CommissionProductRatesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/service_variants/?",
        service.pos.business_commissions.CommissionServiceVariantRatesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/egifts/?",
        service.pos.business_commissions.CommissionEGiftRatesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/memberships/?",
        service.pos.business_commissions.CommissionMembershipRatesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/packages/?",
        service.pos.business_commissions.CommissionPackageRatesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/product_categories/(service_variants|products|egifts|memberships|packages)/?",
        service.pos.business_commissions.CommissionProductCategoriesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/(service_variants|products|egifts|memberships|packages)/(\d+)/?",
        service.pos.business_commissions.CommissionRateDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/mass/resources/(\d+)/(service_variants|products|egifts|memberships|packages)/?",
        service.pos.business_commissions.MassResourceCommissionRateDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/commissions/mass/(service_variants|products|egifts|memberships|packages)/?",
        service.pos.business_commissions.MassCommissionRateDetailsHandler,
    ),
    (r"/business_api/me/businesses/(\d+)/pos/registers/?", service.pos.registers.RegistersHandler),
    (
        r"/business_api/me/businesses/(\d+)/pos/registers/(\d+)/?",
        service.pos.registers.RegisterDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/registers/(\d+)/close/?",
        service.pos.registers.RegisterCloseHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/registers/(\d+)/reopen/?",
        service.pos.registers.RegisterReopenHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/registers/(\d+)/operations/?",
        service.pos.registers.RegisterOperationsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/registers/(\d+)/operations/(\d+)/send/?",
        service.pos.registers.RegisterOperationSendEmailHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/pos/registers/(\d+)/direct_download/?",
        service.pos.registers.RegisterReportDownloadHandler,
    ),
    # BUSINESS PROMOTIONS - HH LM CD
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/promotions/?",
        service.business.service_promotions.PromotionHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/promotions/last_minute/?",
        service.business.service_promotions.LastMinuteHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/promotions/flash_sale/?",
        service.business.service_promotions.FlashSaleHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/promotions/happy_hours/?",
        service.business.service_promotions.HappyHoursHandler,
    ),
    (r"/business_api/me/businesses/(\d+)/appsflyer/?", service.business.appsflyer.AppsFlyer),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/online_booking_integrations/?",
        service.business.other.OnlineBookingIntegrationsStatusListHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/online_booking_integrations/activate/?",
        service.business.other.OnlineBookingIntegrationsActivateHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/online_booking_integrations/deactivate/?",
        service.business.other.OnlineBookingIntegrationsDeactivateHandler,
    ),
    # PURCHASES
    (
        r"/business_api/me/businesses/(\d+)/subscription_listing/?",
        service.purchase.SubscriptionListingHandler,
    ),
    (r"/business_api/me/businesses/(\d+)/subscriptions/?", service.purchase.SubscriptionsHandler),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/listing/?",
        service.purchase.SubscriptionListingHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/invoice/?",
        service.purchase.invoices.InvoiceListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/invoice/send/(\d+)/?",
        service.purchase.invoices.InvoiceEmailHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/offline/request/?",
        service.purchase.OfflineSubscriptionRequestHandler,
    ),
    # PURCHASES - GOOGLE PLAY
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/google_v4/?",
        service.purchase.google.business.GoogleSubscriptionHandlerV4,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/google_v4/restore/?",
        service.purchase.google.business.GoogleRestoreHandlerV4,
    ),
    # PURCHASES - GOOGLE PLAY WEBHOOKS
    (
        r"/google/notifications/renewed/?",
        service.purchase.google.webhook.GoogleWebhookSubscriptionRenewed,
    ),
    (
        r"/google/notifications/canceled/?",
        service.purchase.google.webhook.GoogleWebhookSubscriptionCanceled,
    ),
    (
        r"/google/notifications/restarted/?",
        service.purchase.google.webhook.GoogleWebhookSubscriptionRestarted,
    ),
    (
        r"/google/notifications/expired/?",
        service.purchase.google.webhook.GoogleWebhookSubscriptionExpired,
    ),
    # PURCHASES - APPLE
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/apple/validate/?",
        service.purchase.apple.business.AppleSubscriptionValidationHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/apple/submit/?",
        service.purchase.apple.business.AppleSubscriptionPurchaseHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/apple/task/(\d+)/?",
        service.purchase.apple.business.AppleSusbcriptionTaskStatusHandler,
    ),
    # PURCHASES - APPLE WEBHOOKS
    (
        r"/apple/notifications/renewal/?",
        service.purchase.apple.webhook.AppleWebhookSubscriptionRenewal,
    ),
    (
        r"/apple/notifications/interactive_renewal/?",
        service.purchase.apple.webhook.AppleWebhookSubscriptionInteractiveRenewal,
    ),
    (
        r"/apple/notifications/cancel/?",
        service.purchase.apple.webhook.AppleWebhookSubscriptionCancel,
    ),
    (
        r"/apple/notifications/did_change_renewal_status/?",
        service.purchase.apple.webhook.AppleWebhookSubscriptionDidChangeRenewalStatus,
    ),
    # ATTENDANCE LIST
    (
        r"/business_api/me/businesses/(\d+)/attendance_list/?",
        service.business.attendance_list.AttendanceListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/attendance_list/(\d+)/?",
        service.business.attendance_list.AttendanceListResourceHandler,
    ),
    # PURCHASES - BRAINTREE
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/client_token/?",
        service.purchase.brain_tree.business.BraintreeClientTokenHandler,
    ),
    # DEPRECATED
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/billing_info/?",
        service.purchase.brain_tree.business.BraintreeBillinginfoHandler,
    ),
    # DEPRECATED
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/subscription/?",
        service.purchase.brain_tree.business.BraintreeSubscriptionHandler,
    ),
    # DEPRECATED
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/retry/?",
        service.purchase.brain_tree.business.BraintreeRetryChargeHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/billing_info/async/?",
        service.purchase.brain_tree.business.BraintreeAsyncBillinginfoHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/subscription/async/?",
        service.purchase.brain_tree.business.BraintreeAsyncSubscriptionHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/retry/async/?",
        service.purchase.brain_tree.business.BraintreeAsyncRetryChargeHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/subscriptions/braintree/task_status/([\w-]+)/?",
        service.purchase.brain_tree.business.BraintreeTaskStatusHandler,
    ),
    # obfuscated for iOS - #60967
    (
        r"/business_api/me/businesses/(\d+)/sub/mindjungle/sub/async/?",
        service.purchase.brain_tree.business.ObfuscatedBraintreeAsyncSubscriptionHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/sub/mindjungle/task_status/([\w-]+)/?",
        service.purchase.brain_tree.business.ObfuscatedBraintreeTaskStatusHandler,
    ),
    # BILLING - CONFIG
    (
        r"/business_api/me/businesses/(\d+)/billing/settings/?",
        service.billing.settings.BillingBusinessSettingsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/grant_access_if_possible/?",
        service.billing.settings.BillingBusinessAccessHandler,
    ),
    # BILLING - BRAINTREE
    (
        r"/business_api/me/businesses/(\d+)/billing/braintree/client_token/?",
        service.braintree_app.payment_methods.ClientTokenHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/braintree/billing_info/async/?",
        service.braintree_app.payment_methods.AsyncBillingInfoHandler,
    ),
    # BILLING - SUBSCRIPTION & PAYMENT HANDLING
    (
        r"/business_api/me/businesses/(\d+)/billing/subscription/async/?",
        service.billing.subscription.AsyncSubscriptionHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/invoices/?",
        service.billing.invoices.BillingListInvoicesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/invoices/(\d+)/?",
        service.billing.invoices.BillingInvoiceDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/payment_method/?",
        service.billing.payment.BillingPaymentMethodHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/overdue_details/?",
        service.billing.subscription.OverdueDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/discount_code/?",
        service.billing.discount_code.DiscountCodeHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/cycles/?",
        service.billing.subscription.BillingCyclesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/billing/subscription/retry/async/?",
        service.billing.payment.AsyncRetryChargeHandler,
    ),
    # BILLING - OTHER ENDPOINTS
    (r"/business_api/billing_terms/?", service.billing.terms.BillingTermsAndConditionsHandler),
    # BILLING - STRIPE
    (
        r"/business_api/me/businesses/(\d+)/billing/s/payment_method/async/?",
        service.billing.stripe.payment_method.AsyncStripePaymentMethodHandler,
    ),
    # ANALYTICS
    (
        r"/business_api/me/businesses/(\d+)/segment/counters/?",
        service.segment.counters.CountersHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/segment/booking_counters/?",
        service.segment.counters.BookingCountersHandler,
    ),
    # SCHEDULE
    (
        r"/business_api/me/businesses/(\d+)/shifts/opening_hours/?",
        service.schedule.working_hours.BusinessOpeningHoursHandler,
    ),
    (r"/business_api/me/businesses/(\d+)/shifts/?", service.schedule.working_hours.ScheduleHandler),
    (
        r"/business_api/me/businesses/(\d+)/shifts/copy/?",
        service.schedule.working_hours.ScheduleCopyPasteHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/shifts/resources/(\d+)/?",
        service.schedule.working_hours.ScheduleHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/shifts/resources/(\d+)/working_hours/?",
        service.schedule.working_hours.ResourcesWorkingHoursHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/shifts/resources/(\d+)/schedule/?",
        service.schedule.working_hours.SimpleScheduleHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/shifts/resources/time_offs/?",
        service.schedule.working_hours.BulkTimeOffsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/shifts/resources/(\d+)/time_offs/(\d+)/?",
        service.schedule.working_hours.ResourceTimeOffHandler,
    ),
    # VARIOUS
    (r"/business_api/search/street_hints/?", service.search.region.BusinessStreetHintsHandler),
    (
        r"/business_api/search/street_hints/resolve/?",
        service.search.region.BusinessResolveStreetHandler,
    ),
    (r"/business_api/search/venue/?", service.search.venue.VenueSearchHandler),
    (r"/business_api/search/venue_street/?", service.search.venue_street.VenueStreetSearchHandler),
    (r"/business_api/search/venue_resolver/?", service.search.venue_street.VenueResolverHandler),
    # BUSINESS SCREENS
    (r"/business_api/intro_screens/?", service.intro_screen.handlers.IntroScreensListHandler),
    (r"/business_api/intro_screens/intro/?", service.intro_screen.handlers.IntroScreenListHandler),
    (
        r"/business_api/intro_screens/splash/?",
        service.intro_screen.handlers.SplashScreenListHandler,
    ),
    (
        r"/business_api/intro_screens/welcome_back/?",
        service.intro_screen.handlers.WelcomeBackScreenListHandler,
    ),
    # MARKETPLACE
    (
        r'/business_api/me/businesses/(\d+)/get_notification_text/?',
        service.marketplace.marketplace.GetNotificationText,
    ),
    # BOOST
    (r'/business_api/me/businesses/(\d+)/boost/dashboard/?', service.boost.boost.DashboardHandler),
    # WAREHOUSE
    (
        r"/business_api/me/businesses/(\d+)/warehouse/warehouses/?",
        service.warehouse.warehouses.WarehousesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/warehouses/(\d+)/?",
        service.warehouse.warehouses.WarehouseHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/warehouses/(\d+)/commodities/?',
        service.warehouse.warehouse_commodities.WarehouseCommoditiesHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/brands/?",
        service.warehouse.brands.WarehouseBrandsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/brands/photo/?",
        service.warehouse.brands.WarehouseBrandPhotosHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/brands/(\d+)/?",
        service.warehouse.brands.WarehouseBrandHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/barcode/?',
        service.warehouse.barcode.CommodityBarcodeDetailsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/barcode_generator/?',
        service.warehouse.barcode_generator.CommodityBarcodeGeneratorHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/barcodes/?',
        service.warehouse.barcode_list.CommodityBarcodesListHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/category/?',
        service.warehouse.commodity_category_details.CommodityCategoryDetailsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/categories/?',
        service.warehouse.commodity_category_list.CommodityCategoriesListHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/category_tree/?',
        service.warehouse.commodity_categories_tree.CommodityCategoryTreeHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/commodity/?',
        service.warehouse.commodity_details.CommodityDetailsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/commodity/history/?',
        service.warehouse.commodity_history.CommodityHistoryHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/commodities/?',
        service.warehouse.commodity_list.CommodityListHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/commodity/consumption/?',
        service.warehouse.commodity_consumption.CommodityConsumptionHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/commodities/import/?",
        service.warehouse.commodity_import.CommoditiesImportHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/measures/?',
        service.warehouse.volume_measure.CommodityVolumeMeasureListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/commodity/(\d+)/move/(after|before)/(\d+)/?",
        service.warehouse.commodity_order.WarehouseCommodityChangeOrderHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/commodity/delete/?",
        service.warehouse.commodities_delete.CommodityDeleteHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/commodity/archive/?",
        service.warehouse.commodities_archive.CommodityArchiveHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/supplier/(\d+)/?',
        service.warehouse.supplier.WarehouseSupplierDetailsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/supplier/?',
        service.warehouse.supplier.WarehouseSupplierListHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/commodity/(\d+)/warehouse/(\d+)/history/?',
        service.warehouse.stock_level_history.WarehouseStockLevelHistoryHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/warehouse/photo/?",
        service.warehouse.photo.WarehousePhotoHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/documents/?',
        service.warehouse.document.GenericDocumentsListHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/documents/(\d+)/?',
        service.warehouse.document.GenericDocumentsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/documents/quick_order/?',
        service.warehouse.quick_order.QuickOrderHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/formula/?',
        service.warehouse.formula.WarehouseRecipesHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/internal_expenditure_reasons/?',
        service.warehouse.internal_expenditure_reason.InternalExpenditureReasonsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/warehouse/wholesaler/?',
        service.warehouse.wholesalers.WholesalersHandler,
    ),
    # MARKET PAY
    (
        r"/business_api/me/businesses/(\d+)/market_pay/account/?",
        service.market_pay.account_holder.AccountHolderHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/market_pay/account/turn_on_pba/?",
        service.market_pay.account_holder.PBAHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/market_pay/next_payout/?",
        service.market_pay.account_holder.NextPayoutHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/market_pay/payouts/?",
        service.market_pay.account_holder.BusinessPayoutsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/market_pay/payout/(\d+)/?",
        service.market_pay.account_holder.BusinessPayoutDetailsHandler,
    ),
    # STRIPE
    (
        r"/business_api/me/businesses/(\d+)/stripe/config/?",
        service.stripe_integration.account.StripeConfigHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/account/?",
        service.stripe_integration.account.StripeAccountHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/account_link/?",
        service.stripe_integration.account.StripeAccountLinkHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/reader/?",
        service.stripe_integration.reader.StripeReaderHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/reader/([-\w]+)/label/?",
        service.stripe_integration.reader.StripeReaderLabelHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/readers/?",
        service.stripe_integration.reader.StripeReadersHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/connection_token/?",
        service.stripe_integration.reader.StripeConnectionTokenHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/payment_intent/?",
        service.stripe_integration.checkout.StripePaymentIntentHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/stripe/payment_intent/(\d+)/action/?",
        service.stripe_integration.checkout.StripePaymentIntentActionHandler,
    ),
    # CONSENTS
    (
        r'/business_api/me/businesses/(\d+)/consent_forms/?',
        service.consents.consents.BusinessConsentFormsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consent_forms/(\d+)/?',
        service.consents.consents.BusinessConsentFormHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consent_forms/(\d+)/pdf/?',
        service.consents.consents.BusinessConsentFormPdfHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/?',
        service.consents.consents.BusinessConsentsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/([-\w]+)/?',
        service.consents.consents.BusinessConsentHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/([-\w]+)/sign/?',
        service.consents.consents.BusinessConsentSignHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/([-\w]+)/signature/?',
        service.consents.consents.BusinessConsentSignatureHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/([-\w]+)/photo/?',
        service.consents.consents.BusinessConsentPhotoHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/([-\w]+)/pdf/?',
        service.consents.consents.BusinessConsentPdfHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/([-\w]+)/notify/?',
        service.consents.consents.BusinessConsentNotifyHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/consents/([-\w]+)/send_email/?',
        service.consents.consents.BusinessConsentSendEmailHandler,
    ),
    # METRICS
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/metrics/?",
        service.metrics.business_application.BusinessApplicationsHandler,
    ),
    # B2b REFERRAL
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/referral/?",
        service.b2b_referral.b2b_referral.ReferralListingHandler,
    ),
    (
        r"/business_api/referral_code/(?P<code>\w+)/?",
        service.b2b_referral.b2b_referral.DeprecatedReferralCodeValidationHandler,
    ),
    (
        r"/business_api/referral_code/?",
        service.b2b_referral.b2b_referral.ReferralCodeBodyValidationHandler,
    ),
    (
        r"/business_api/referral_terms/?",
        service.b2b_referral.b2b_referral.ReferralTermsAndConditionsHandler,
    ),
    # Safety Rules
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/safety_rules/?",
        service.business_related.safety_rules.BusinessSafetyRulesHandler,
    ),
    # Amenities
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/amenities/?",
        service.business_related.amenities.BusinessAmenitiesHandler,
    ),
    # INVOICING
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/invoicing/invoice/?",
        service.invoicing.invoice.InvoiceListHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/invoicing/invoice/(?P<invoice_id>\d+)/?",
        service.invoicing.invoice.InvoiceDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/invoicing/invoice/(?P<invoice_id>\d+)/correction/?",
        service.invoicing.invoice.CorrectingInvoiceHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/invoicing/invoice/(?P<invoice_id>\d+)/preview/?",
        service.invoicing.invoice.InvoicePdfPreviewHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/invoicing/invoice/(?P<invoice_id>\d+)/send_to_buyer/?",
        service.invoicing.invoice.InvoiceSendToBuyerHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/invoicing/invoice/next_number/?",
        service.invoicing.invoice.InvoiceNextNumberHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/invoicing/buyers/?",
        service.invoicing.buyers.InvoicingBuyersHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/buyers/?",
        service.invoicing.buyers.BCIBuyersListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/customers/(\d+)/buyers/?",
        service.invoicing.buyers.InvoicingBCIBuyersHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/invoicing/buyers/(\d+)/?",
        service.invoicing.buyers.InvoicingBuyerHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/invoicing/seller/?",
        service.invoicing.sellers.InvoicingSellerHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/invoicing/cash_register_document/?",
        service.invoicing.cash_register_document.CashRegisterDocumentListHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/invoicing/cash_register_document/(\d+)/?",
        service.invoicing.cash_register_document.CashRegisterDocumentDetailsHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/invoicing/cash_register_document/(\d+)/preview/?",
        service.invoicing.cash_register_document.CashRegisterDocumentPdfPreviewHandler,
    ),
    # MESSAGE BLAST
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/?",
        service.message_blast.message_blast.MessageBlastTemplateListingHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/cost_estimation/?",
        service.message_blast.message_blast.MessageBlastCostEstimationHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/content/?",
        service.message_blast.message_blast.MessageBlastContentTemplateHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/stop_code/?",
        service.message_blast.message_blast.MessageBlastStopCodeHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/group/(?P<internal_name>[-\w]+)/?",
        service.message_blast.message_blast.MessageBlastTemplateGroupHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/image/?",
        service.message_blast.images.MessageBlastCustomImageHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/images/?",
        service.message_blast.images.DeprecatedMessageBlastTemplateImageHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/scheduled/?",
        service.message_blast.message_blast.MessageBlastScheduledHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/scheduled/(?P<message_blast_id>\d+)/?",
        service.message_blast.message_blast.MessageBlastScheduledDeleteHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/template_images/?",
        service.message_blast.images.MessageBlastTemplateImageHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/template_images/all_images/?",
        service.message_blast.images.MessageBlastAllTemplateImageHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/template_images/category_tree/?",
        service.message_blast.images.MessageBlastImageCategoryTreeHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/(?P<template_id>\d+)/one_time_send/?",
        service.message_blast.message_blast.OneTimeMessageBlastHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/(?P<template_id>\d+)/one_time_send_test/?",
        service.message_blast.message_blast.MessageBlastTestSendHandler,
    ),
    # DEPRECATED
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/counter/?",
        service.message_blast.message_blast.DeprecatedMessageBlastCustomerCounterHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/activate_recommended/?",
        service.message_blast.message_blast.DeprecatedActivateRecommendedMessageBlastHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/(?P<template_id>\d+)/cost_estimation/?",
        service.message_blast.message_blast.DeprecatedManualMessageBlastCostEstimationHandler,
    ),
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/activate/?",
        service.message_blast.message_blast.DeprecatedActivateMessageBlastHandler,
    ),
    # REGEX TOO BROAD TO MOVE UP
    (
        r"/business_api/me/businesses/(?P<business_id>\d+)/message_blast/(?P<template_id>\w+)/?",
        service.message_blast.message_blast.MessageBlastTemplateHandler,
    ),
    ############################################################
    #######################   OTHER API  #######################
    ############################################################
    # GEOCODING
    (r"/utils/geocode/?", service.other.geocoding.GeocodingHandler),
    (r"/utils/reverse_geocode/?", service.other.geocoding.ReverseGeocodingHandler),
    (
        r"/utils/phone_check/?",
        service.other.utils.CheckPhoneHandlerHandler,
    ),
    (r"/other/apple_token/?", service.other.apple_maps.AppleTokenHandler),
    (
        r"/(?P<booking_param>\w+)/password_reset/(?P<user_id>\d+)/(?P<password_reset_token>.*?)/?",
        service.other.website.PasswordResetHandler,
    ),
    (
        r"/country-(?P<x_api_country>\w+)/(?P<booking_param>\w+)/change_email/(?P<user_id>\d+)/(?P<new_email>.*?)/(?P<email_change_token>.*?)/?",
        service.other.website.ChangeEmailHandler,
    ),
    (
        r"/(?P<booking_param>\w+)/change_email/(?P<user_id>\d+)/(?P<new_email>.*?)/(?P<email_change_token>.*?)/?",
        service.other.website.ChangeEmailHandler,
    ),
    # STATIC CONTENT
    (r"/pages/([-\w]+)/?", service.other.static_content.StaticPageHandler),
    # REGION
    (r"/location/region_info/?", service.search.region.RegionInfoHandler),
    # PARTNERS API
    # PARTNERS-BUSINESS API v3 FBE
    (
        r"/business_api/me/facebook/v3/dialog_oauth/?",
        service.partners.facebook.v3.handler.FacebookFBEOauthUrlHandler,
    ),
    (
        r"/business_api/me/facebook/v3/status/?",
        service.partners.facebook.v3.handler.FacebookFBEStatusUrlHandler,
    ),
    (
        r"/business_api/me/facebook/v3/extras/?",
        service.partners.facebook.v3.handler.FacebookFBEExtrasHandler,
    ),
    (
        r"/business_api/me/facebook/v3/token/?",
        service.partners.facebook.v3.handler.FacebookFBETokenHandler,
    ),
    (
        r"/business_api/me/facebook/v3/webhook/?",
        service.partners.facebook.v3.handler.FacebookFBEWebhookHandler,
    ),
    # GROUPON
    (
        r"/groupon/v1/system/availability/?",
        service.partners.groupon.v1.handler.GrouponHeartbeatHandler,
    ),
    # Experiments API
    (
        r"/experiment/(\w+)/experiment_variant/([\-\w+]*)/?",
        service.experiment_v3.named_experiment.NamedExperimentHandler,
    ),
    (
        r"/business_api/me/businesses/(\d+)/experiment_variant/([\-\w+]*)/?",
        service.experiment_v3.business_experiment.BusinessExperimentHandler,
    ),
    # ZOOM
    (
        r"/business_api/me/zoom/meeting/(?P<meeting_uuid>.*?)/start/?",
        service.other.zoom.StartZoomMeetingDetailsHandler,
    ),
    (
        r'/business_api/me/businesses/(\d+)/meeting/(\d+)/?',
        service.other.zoom.ZoomMeetingDetailsHandler,
    ),
    # PRINTER API
    (r"/printer_api/business/login/?", webapps.printer_api.login.PrinterAccountLoginHandler),
    (r"/printer_api/business/(\d+)/config/?", webapps.printer_api.config.PrinterConfigHandler),
    (r"/printer_api/business/(\d+)/printouts/?", webapps.printer_api.printers.PrintoutListHandler),
    (
        r"/printer_api/business/(\d+)/printouts/(\d+)/?",
        webapps.printer_api.printers.PrintoutReceiptHandler,
    ),
    ### BOOKSY AUTH WEBHOOKS ###
    (
        r"/booksy_auth/update_user/?",
        service.booksy_auth.webhook.ActualizeUserBooksyAuthWebhookHandler,
    ),
    # Routing
    (r"/routing/server_time/?", service.other.server_time.ServerTimeHandler),
    # Google - v3
    # Groupon
    (r"/groupon/v1/bookings/?", service.partners.groupon.v1.booking.GrouponCreateBookingHandler),
    (
        r"/groupon/v1/bookings/cancellations/?",
        service.partners.groupon.v1.booking.GrouponCancelBookingHandler,
    ),
    (
        r"/groupon/v1/feed/services/availability/?",
        service.partners.groupon.v1.availability.GrouponServicesProxyRequestHandler,
    ),
    (
        r"/groupon/v1/feed/services/merchants/?",
        service.partners.groupon.v1.availability.GrouponMerchantsProxyRequestHandler,
    ),
    (
        r"/groupon/v1/bookings/availability/?",
        service.partners.groupon.v1.availability.GrouponCheckAvailabilityProxyRequestHandler,
    ),
    # warehouse
    (r"/choices/?", service.other.warehouse.ChoicesHandler),
    # Push Notifications
    (r"/push/send/?", service.other.push.PushTestHandler),
    # ADYEN
    (r"/marketpay/notifications/?", webapps.market_pay.views.NotificationsHandler),
    # DEBUG
    (r"/debug/exception/tornado/?", service.other.debug.DebugExceptionHandler),
    (r"/debug/celery_exception/?", service.other.debug.DebugCeleryExceptionHandler),
    (r"/debug/timeout/tornado/?", service.other.debug.DebugTimeoutHandler),
    (r"/debug/sleep/tornado/(?P<seconds>\d+)/?", service.other.debug.SleepHandler),
    (r"/health_check/?", service.other.debug.HealthCheckHandler),
    # CELERY
    (r"/celery/task/(?P<task_id>.*?)/?", service.other.celery.CeleryTaskStatusHandler),
    # physiotherapy
    (
        r"/fizjo/business/(?P<business_id>\d+)/customer/(?P<customer_id>\d+)/?",
        service.other.redirects.PhysiotherapyHistoryRedirectHandler,
    ),
    (r"/visit/(?P<redirect_id>\d+)/?", service.other.redirects.VisitRedirectHandler),
    # TWILIO STATUS CALLBACKS
    (r"/twilio/status/?", service.notification.webhook.TwilioTriggersWebhookHandler),
    # ITERABLE
    (r"/iterable/status/?", service.notification.webhook.IterableWebhookHandle),
    # Telnyx
    (r"/telnyx/status/?", service.notification.webhook.TelnyxWebhookHandler),
    # EVOX
    (r"/evox/status/?", service.notification.webhook.EvoxWebhookHandler),
    # waitlist
    (r"/waitlist/?", service.other.wait_list.WaitListHandler),
    # CONTACT_FORM
    (r"/customer_api/contact_us/?", service.other.contact_us.handler.ContactUsHandler),
    # CloudFront proxy
    (
        r"/cloudfront_proxy/(?P<region>[\w-]+)/(?P<path>.+)",
        service.other.cloudfront.CloudFrontImageProxyHandler,
    ),
    ############################################################
    ###################  DEV ONLY HANDLERS  ####################
    ############################################################
    # those are not prefixed with /api/cc/2/ and normally
    # is handled not by api, but by nginx or other projects (booksy-route)
    # STATICS
    (
        r"^/%s/(.*)" % settings.STATIC_URL.strip('/'),
        tornado.web.StaticFileHandler,
        {'path': settings.STATICS_DIR},
    ),
    (
        r"^/favicon.ico()",
        tornado.web.StaticFileHandler,
        {'path': os.path.join(settings.STATICS_DIR, 'img/favicon.ico')},
    ),
    # MEDIA
    (r'^/media/(.*)', tornado.web.StaticFileHandler, {'path': settings.MEDIA_ROOT}),
    ############################################################
    ####################  404 not found  #######################
    ############################################################
    (
        r'/other/without_ff/?',
        service.other.feature_flags.NoFeatureFlagHandler,
    ),
    (
        r'/other/with_ff/?',
        service.other.feature_flags.FeatureFlagHandler,
    ),
    (r"^.*", service.error.ErrorNotFoundHandler),
]

api_docs_url_handlers = [
    (r"/docs/?", service.other.docs.APIDocsHandler),
    (r"/docs/ChangeLog/?", service.other.docs.ChangeLogHandler),
    (r"/docs/api/?", service.other.docs.APIDocsDataHandler),
    (r"/docs/api/(\w*)/?", service.other.docs.APIDocsDataHandler),
    # PARTNERS API DOCS
    (r"/facebook_api/docs/?", service.other.docs.APIDocsHandler),
    # (r"/partners/docs/ChangeLog/?", service.other.docs.ChangeLogHandler),
    (r"/facebook_api/docs/api/?", service.other.docs.APIPartnersDocsDataHandler),
    (r"/facebook_api/docs/api/(\w*)/(\w*)/?", service.other.docs.APIPartnersDocsDataHandler),
    # PRINTER API DOCS
    (r"/printer_api/docs/?", service.other.docs.APIPrinterDocsDataHandler),
    (r"/printer_api/docs/api/?", service.other.docs.APIPrinterDocsDataHandler),
    (r"/printer_api/docs/api/(\w*)/(\w*)/?", service.other.docs.APIPrinterDocsDataHandler),
]

if not LIVE_DEPLOYMENT:
    url_handlers = api_docs_url_handlers + raw_url_handlers
else:
    url_handlers = raw_url_handlers

# PATCH URL PATTERNS TO SUPPORT /us/api/2/... URL PATTERNS
_patterns = []
prefixes = [
    fr'/core/v{versions.VERSION}',  # default
    *(fr'/core-{subservice}/v{versions.VERSION}' for subservice in settings.API_SUBSERVICES),
    fr'/api/{settings.API_COUNTRY}/{versions.VERSION}',  # legacy prefix
]
prefix_matcher = '|'.join(prefixes)

for row in url_handlers:
    if len(row) == 2:
        pattern, handler = row
        params = {}
    else:
        pattern, handler, params = row
    assert any(pattern.endswith(ending) for ending in ["/?", ")", "*"]), (
        "All URL patterns must allow trailing / (check: %s)" % pattern
    )

    # automagically fill profile_type
    if '/customer_api/' in pattern:
        params['profile_type'] = 'C'  # UserProfile.Type.CUSTOMER
    elif '/business_api' in pattern:
        params['profile_type'] = 'B'  # UserProfile.Type.BUSINESS

    if pattern.startswith('^') or not issubclass(handler, BaseBooksyRequestHandler):
        # do not add prefix
        prefix = ''
    else:
        # prefix all API handlers with (/core/v{vv}/|/api/{cc}/{v}/)
        if '?P<' in pattern:
            prefix = fr'^(?P<prefix>{prefix_matcher})'
        else:
            prefix = fr'^({prefix_matcher})'
    _patterns.append((prefix + pattern, handler, params))

url_handlers = _patterns
# ENDOF PATCH URL PATTERNS


def get_uwsgi_application():
    """Main application Factory"""
    if 'debug' not in options._options:
        define('debug', settings.DEBUG)
    if settings.DEBUG:
        enable_pretty_logging(logger=logging.getLogger('tornado'))
    options.logging = logging.getLevelName(logging.WARNING)
    options.log_to_stderr = True
    application = BooksyWSGIApplication(
        url_handlers,
        debug=settings.DEBUG,
        autoreload=False,  # handled by uwsgi
    )
    wsgi_app = BooksyWSGIAdapter(application)
    return wsgi_app


def get_tornado_application():
    if 'debug' not in options._options:
        define('debug', settings.DEBUG)
    options.logging = 'info'
    options.log_to_stderr = True
    if settings.LOGGING_USE_STDOUT:
        logging.getLogger('tornado.application').disabled = True
    enable_pretty_logging(logger=logging.getLogger('tornado'))
    return BooksyWSGIApplication(url_handlers, debug=settings.DEBUG)


if settings.RUN_MODE == 'uwsgi':
    tornado_app = get_uwsgi_application()
elif settings.RUN_MODE == 'tornado':
    tornado_app = get_tornado_application()
else:
    raise RuntimeError(f'unknown run mode {settings.RUN_MODE}')


if settings.API_PRIVATE:
    from wsgi_proxy import TornadoDjangoWsgi
    from wsgi import django_app

    application = TornadoDjangoWsgi(django_app=django_app, tornado_app=tornado_app)
