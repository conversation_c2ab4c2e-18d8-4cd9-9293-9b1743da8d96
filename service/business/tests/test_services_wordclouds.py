import pytest
from django.conf import settings
from mock import patch
from model_bakery import baker
from pytest import approx

from service.business.service_suggestion import (
    ServicesWordcloudsHandler,
    get_services_suggestion_by_category,
)
from service.tests import BaseAsyncHTTPTest
from webapps.business.models import BusinessCategory, ServicesWordclouds


def _compare_values(value1, value2):
    assert value1 == approx(value2, rel=10 ** (-4))


@pytest.mark.django_db
def test_get_services_suggestion_by_category():
    category = baker.make(BusinessCategory)
    category2 = baker.make(BusinessCategory)

    cloud1 = baker.make(
        ServicesWordclouds,
        category=category,
        country=settings.API_COUNTRY,
        order=1,
        name='a',
    )
    cloud2 = baker.make(
        ServicesWordclouds,
        category=category2,
        country=settings.API_COUNTRY,
        order=3,
        name='c',
    )
    cloud3 = baker.make(
        ServicesWordclouds,
        category=category2,
        country=settings.API_COUNTRY,
        order=2,
        name='a',
    )
    baker.make(
        ServicesWordclouds,
        category=baker.make(BusinessCategory),
        country=settings.API_COUNTRY,
    )
    baker.make(
        ServicesWordclouds,
        category=category,
        country='aa',
    )
    result = get_services_suggestion_by_category([category.id, category2.id])
    assert len(result) == 3

    assert result[0]['name'] == cloud1.name
    _compare_values(result[0]['price'], cloud1.price)
    assert result[0]['duration'] == cloud1.duration
    assert result[0]['wordcloud_id'] == cloud1.id

    assert result[1]['name'] == cloud3.name
    _compare_values(result[1]['price'], cloud3.price)
    assert result[1]['duration'] == cloud3.duration
    assert result[1]['wordcloud_id'] == cloud3.id

    assert result[2]['name'] == cloud2.name
    _compare_values(result[2]['price'], cloud2.price)
    assert result[2]['duration'] == cloud2.duration
    assert result[2]['wordcloud_id'] == cloud2.id


@pytest.mark.django_db
def test_get_services_suggestion_by_category_no_categories():
    result = get_services_suggestion_by_category([666])
    assert not result


class ServicesWordcloudsHandlerTestCase(BaseAsyncHTTPTest):

    @patch.object(ServicesWordcloudsHandler, 'business_with_manager')
    @patch('service.business.service_suggestion.get_services_suggestion_by_category')
    @pytest.mark.django_db
    def test_get_wordclouds(self, get_wc_by_categ_mock, biz_with_manager_mock):
        clouds_return = [
            {'duration': 10, 'name': 'name1', 'price': 20, 'wordcloud_id': 1},
            {'duration': 30, 'name': 'name2', 'price': 50, 'wordcloud_id': 2},
            {'duration': 30, 'name': 'name2', 'price': 50, 'wordcloud_id': 3},
        ]
        get_wc_by_categ_mock.return_value = clouds_return
        biz_with_manager_mock.return_value = self.business

        category1 = baker.make(BusinessCategory)
        category2 = baker.make(BusinessCategory)
        baker.make(BusinessCategory)
        primary_category = baker.make(BusinessCategory)
        self.business.categories.add(category1, category2)
        self.business.primary_category = primary_category
        self.business.save()

        url = '/business_api/me/businesses/0/service_wordclouds/'
        resp = self.fetch(url)

        assert resp.json['services_wordclouds'] == clouds_return[:2]
        get_wc_by_categ_mock.assert_called_once_with([category1.id, category2.id])
