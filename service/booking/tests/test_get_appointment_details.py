from datetime import timedelta

import pytest
from model_bakery import baker

from lib.tools import tznow
from webapps.booking.tests.utils import create_appointment

from service.tests import BaseAsyncHTTPTest


@pytest.mark.django_db
@pytest.mark.usefixtures('clean_index_class_fixture')
class GetAppointmentDetailsTestCase(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.customer = baker.make(
            'BusinessCustomerInfo',
            business=self.business,
            user=self.user,
        )
        self.booked_from = tznow()
        self.booked_till = tznow() + timedelta(minutes=15)
        self.appointment = self.create_appointment()
        self.url = (
            f'/business_api/me/businesses/{self.business.id}/appointments/{self.appointment.id}'
        )

    def test_simple_get(self):
        self.customer.reindex()
        resp = self.fetch(self.url, method='GET')

        self.assertEqual(200, resp.code)

        r_appointment = resp.json['appointment']
        self.assertTrue(r_appointment)
        self.assertTrue(r_appointment['appointment_id'])
        self.assertTrue(r_appointment['booked_from'])
        self.assertTrue(r_appointment['booked_till'])
        self.assertTrue(r_appointment['status'])
        self.assertTrue(r_appointment['type'])
        self.assertTrue(r_appointment['appointment_type'])
        self.assertTrue(r_appointment['subbookings'])
        self.assertFalse(r_appointment['is_booksy_gift_card_appointment'])

        r_customer = resp.json['customer']
        self.assertTrue(r_customer)
        self.assertEqual(self.customer.id, r_customer['_id'])
        self.assertEqual(self.appointment.id, r_customer['last_appointment_id'])
        self.assertEqual(1, r_customer['booking_count'])

    def test_latest_appointment_uid(self):
        shifted_booked_till = self.appointment.booked_till + timedelta(minutes=15)
        latest_appointment = self.create_appointment(
            self.appointment.booked_till, shifted_booked_till
        )
        self.customer.reindex()

        resp = self.fetch(self.url, method='GET')

        self.assertEqual(200, resp.code)

        r_appointment = resp.json['appointment']
        self.assertTrue(r_appointment)

        r_customer = resp.json['customer']
        self.assertTrue(r_customer)
        self.assertEqual(self.customer.id, r_customer['_id'])
        self.assertEqual(latest_appointment.id, r_customer['last_appointment_id'])
        self.assertEqual(2, r_customer['booking_count'])

    def create_appointment(self, booked_from=None, booked_till=None):
        subbooking_dict = {
            "staffer": self.owner,
            "booked_from": booked_from or self.booked_from,
            "booked_till": booked_till or self.booked_till,
        }
        return create_appointment(
            [subbooking_dict], business=self.business, booked_for=self.customer
        )

    def test_get_appointment_details_bgk_true(self):
        self.appointment.is_booksy_gift_card_appointment = True
        self.appointment.save()
        resp = self.fetch(self.url, method='GET')
        self.assertEqual(200, resp.code)
        self.assertTrue(resp.json['appointment']['is_booksy_gift_card_appointment'])

    def test_get_appointment_details_bgk_default_false(self):
        resp = self.fetch(self.url, method='GET')
        self.assertEqual(200, resp.code)
        self.assertFalse(resp.json['appointment']['is_booksy_gift_card_appointment'])
