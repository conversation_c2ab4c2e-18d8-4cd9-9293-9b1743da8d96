from unittest import mock

import pytest
from model_bakery import baker

from webapps.b2b_referral.helpers import B2BReferralDetails
from webapps.b2b_referral.models import B2BReferralCode
from webapps.b2b_referral.serializers import B2BReferralCodeSerializer
from webapps.business.models import Business


@mock.patch.object(
    B2BReferralDetails,
    'get_referral_photo',
    lambda *_: 'placki.jpg',
)
@mock.patch.object(
    B2BReferralDetails,
    'get_referral_amount_txt',
    lambda *_: '$123',
)
@pytest.mark.django_db
def test_b2b_referral_code_serializer():
    business = baker.make(Business)
    b2b_referral_code = B2BReferralCode.generate_code(business)

    serializer = B2BReferralCodeSerializer(
        instance=b2b_referral_code,
    )
    data = serializer.data

    assert data['name'] == business.name
    assert data['referral_photo'] == 'placki.jpg'
    assert data['referral_reward'] == '$123'
