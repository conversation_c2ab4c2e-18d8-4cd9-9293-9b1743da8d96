import tornado.web
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from stripe.error import InvalidRequestError

from service.tools import RequestHandler, json_request, session
from webapps.pos.models import POS
from webapps.stripe_integration.exceptions import ReaderNotFound
from webapps.stripe_integration.provider import StripeProvider
from webapps.stripe_integration.serializers import (
    StripeReaderLabelSerializer,
    StripeReaderSerializer,
)
from webapps.stripe_integration.terminal import get_location_readers


class StripeReaderHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @json_request
    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Register new Stripe terminal Reader
            notes: >
                Dedicated <b>only for internet readers</b>! According to the Stripe documentation,
                bluetooth readers are registered in Stripe API automatically during the connection,
                if location id is provided
                https://stripe.com/docs/terminal/readers/connecting/bbpos-chipper2xbt#connect-reader
                <br><br>
                Structure of the response object is taken from
                https://stripe.com/docs/api/terminal/readers/object

            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: body
                  type: StripeReaderRequest
                  paramType: body
            type: StripeReaderObject
        :swagger
        swaggerModels:
            StripeReaderRequest:
                id: StripeReaderRequest
                properties:
                    registration_code:
                        type: string
                        required: true
                        description: pos terminal registration code
                    label:
                        type: string
                        required: true
                        description: pos terminal label
            StripeReaderObject:
                id: StripeReaderObject
                properties:
                    id:
                        type: string
                    device_type:
                        type: string
                        enum: [verifone_P400, bbpos_chipper2x]
                    ip_address:
                        type: string
                    label:
                        type: string
                    location:
                        type: string
                    serial_number:
                        type: string
                    status:
                        type: string
        :swaggerModels

        """
        if not (settings.POS and settings.POS__STRIPE_TERMINAL):
            raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND)

        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_staffer(pos.business)
        stripe_account = pos.stripe_account
        if not stripe_account:
            raise tornado.web.HTTPError(status.HTTP_400_BAD_REQUEST)

        serializer = StripeReaderSerializer(
            data=self.data,
            context={
                'stripe_account': stripe_account,
            },
        )
        data = self.validate_serializer(serializer)

        try:
            reader = StripeProvider.create_reader(
                label=data['label'],
                registration_code=data.get('registration_code'),
                external_location_id=data.get('external_location_id'),
            )
        except InvalidRequestError:
            # invalid registration_code
            return self.quick_error(
                ('invalid_registration_code', 'validation', 'registration_code'),
                _("Incorrect code. Please try again"),
            )

        return self.finish_with_json(
            status.HTTP_200_OK,
            {"reader": reader},  # TODO maybe we should use our own serializer
        )


class StripeReaderLabelHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @json_request
    @session(login_required=True)
    def put(self, business_id, reader_id):
        """
        swagger:
            summary: Edit label of a reader.
            notes: >
                It is possible to change a label of any type of reader (bbpos & p400). Reader has
                to be registered to the users location, otherwise 404 will be returned.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: reader_id
                  type: string
                  paramType: path
                  description: Stripe reader id (starts with "tmr_")
                - name: body
                  type: StripeReaderRequest
                  paramType: body
        :swagger
        swaggerModels:
            StripeReaderRequest:
                id: StripeReaderRequest
                properties:
                    label:
                        type: string
                        required: true
                        description: pos terminal label
        :swaggerModels

        """
        if not settings.POS:
            return self.finish_with_json(
                status.HTTP_400_BAD_REQUEST,
                {'error': 'Business has no POS'},
            )

        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_staffer(pos.business)
        stripe_account = pos.stripe_account
        if not stripe_account:
            raise tornado.web.HTTPError(status.HTTP_400_BAD_REQUEST)

        serializer = StripeReaderLabelSerializer(data=self.data)
        label = self.validate_serializer(serializer)['label']

        try:
            StripeProvider.update_stripe_reader_label(
                stripe_account=stripe_account,
                reader_id=reader_id,
                label=label,
            )
        except ReaderNotFound as e:
            raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND) from e

        self.finish_with_json(status.HTTP_200_OK, {})


class StripeReadersHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get list of registered Readers
            notes: >
                Structure of the response object is taken from
                https://stripe.com/docs/api/terminal/readers/object
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
            type: StripeReadersResponse
        :swagger
        swaggerModels:
            StripeReadersResponse:
                id: StripeReadersResponse
                properties:
                    readers:
                        type: array
                        description: reader objects
                        items:
                            type: StripeReaderObject
            StripeReaderObject:
                id: StripeReaderObject
                properties:
                    id:
                        type: string
                    device_type:
                        type: string
                        enum: [verifone_P400, bbpos_chipper2x]
                    ip_address:
                        type: string
                    label:
                        type: string
                    location:
                        type: string
                    serial_number:
                        type: string
                    status:
                        type: string
        :swaggerModels
        """
        if not (settings.POS and settings.POS__STRIPE_TERMINAL):
            raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND)

        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_staffer(pos.business)
        stripe_account = pos.stripe_account

        if not stripe_account:
            raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND)

        stripe_location = StripeProvider.get_location(stripe_account)

        if not stripe_location:
            return self.finish_with_json(
                status.HTTP_400_BAD_REQUEST, {"error": _("This account has no location!")}
            )

        location_readers = get_location_readers(stripe_location)
        return self.finish_with_json(status.HTTP_200_OK, {"readers": location_readers})


class StripeConnectionTokenHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True)
    def get(self, business_id):
        """Get stripe connection token.

        swagger:
            summary: Get stripe connection token
            type: StripeAccountResponse
            parameters:
              - name: business_id
                description: Business id
                type: integer
                required: true
                paramType: path
        :swagger
        """
        if not (settings.POS and (settings.POS__STRIPE_TERMINAL or settings.POS__TAP_TO_PAY)):
            raise tornado.web.HTTPError(status.HTTP_404_NOT_FOUND)

        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_staffer(pos.business)

        token = StripeProvider.create_connection_token()

        return self.finish_with_json(
            status.HTTP_200_OK,
            {
                'connection_token': token.secret,
            },
        )
