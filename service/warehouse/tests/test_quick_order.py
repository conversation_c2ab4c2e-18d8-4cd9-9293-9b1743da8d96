import datetime
import json
from decimal import Decimal
from unittest.mock import patch

import pytest
from model_bakery import baker
from rest_framework import status

from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps.business.models import Resource
from webapps.warehouse.models import (
    BaseWarehouseDocument,
    Commodity,
    CommodityStockLevel,
    Supplier,
    Supply,
    Warehouse,
    WarehouseDocumentType,
)


@pytest.mark.django_db
class QuickOrderHandler(BaseAsyncHTTPTest):
    # pylint: disable=line-too-long
    url = '/business_api/me/businesses/{business_id}/warehouse/documents/quick_order/'

    def setUp(self):
        super().setUp()
        self.staffer = baker.make(Resource, business=self.business, type=Resource.STAFF)

        # Warehouses:
        self.warehouse_a = baker.make(Warehouse, business=self.business)
        self.warehouse_b = baker.make(Warehouse, business=self.business)

        # Suppliers:
        self.supplier_a = baker.make(Supplier, business=self.business, name='A')
        self.supplier_b = baker.make(Supplier, business=self.business, name='B')

        # Commodities:
        self.commodity_a = baker.make(
            Commodity,
            name='A',
            business=self.business,
            total_pack_capacity=10,
        )
        self.commodity_b = baker.make(
            Commodity,
            name='B',
            business=self.business,
            total_pack_capacity=10,
        )

        # Supplier A delivers only commodity A
        self.supplier_a.commodities.add(self.commodity_a)

        # Supplier B delivers only commodity B
        self.supplier_b.commodities.add(self.commodity_b)

        baker.make(
            CommodityStockLevel,
            commodity=self.commodity_a,
            warehouse=self.warehouse_a,
            maximum_packages=10,  # 100 volume
            remaining_volume=90,  # one package can be ordered
        )
        baker.make(
            CommodityStockLevel,
            commodity=self.commodity_a,
            warehouse=self.warehouse_b,
            maximum_packages=10,  # 100 volume
            remaining_volume=60,  # 4 packages should be ordered
        )
        baker.make(
            CommodityStockLevel,
            commodity=self.commodity_b,
            warehouse=self.warehouse_b,
            maximum_packages=10,  # 100 volume
            remaining_volume=72,  # 3 packages should be ordered
        )

    def tearDown(self):
        BaseWarehouseDocument.objects.all().delete()
        return super().tearDown()

    def test_get(self):
        url = self.url.format(business_id=self.business.id)
        url += '?suppliers={}&suppliers={}'.format(
            self.supplier_a.id,
            self.supplier_b.id,
        )

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert len(resp.json['orders']) == 2

        order = resp.json['orders'][0]
        assert order['supplier'] == self.supplier_a.id
        assert len(order['rows']) == 2

        order_row_1 = next(
            filter(lambda x: x['warehouse'] == self.warehouse_a.id, order['rows']), None
        )
        assert order_row_1
        assert order_row_1['commodity'] == self.commodity_a.id
        assert order_row_1['quantity'] == 1

        order_row_2 = next(
            filter(lambda x: x['warehouse'] == self.warehouse_b.id, order['rows']), None
        )
        assert order_row_2['commodity'] == self.commodity_a.id
        assert order_row_2['quantity'] == 4

        order_to_b = resp.json['orders'][1]
        assert order_to_b['supplier'] == self.supplier_b.id
        assert len(order_to_b['rows']) == 1

        order_b_row = order_to_b['rows'][0]
        assert order_b_row['commodity'] == self.commodity_b.id
        assert order_b_row['warehouse'] == self.warehouse_b.id
        assert order_b_row['quantity'] == 3

    def test_get_without_deleted_commodities(self):
        self.commodity_a.delete()
        url = self.url.format(business_id=self.business.id)
        url += '?suppliers={}&suppliers={}'.format(
            self.supplier_a.id,
            self.supplier_b.id,
        )

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert len(resp.json['orders']) == 1

        order_to_b = resp.json['orders'][0]
        assert order_to_b['supplier'] == self.supplier_b.id
        assert len(order_to_b['rows']) == 1

        order_b_row = order_to_b['rows'][0]
        assert order_b_row['commodity'] == self.commodity_b.id
        assert order_b_row['warehouse'] == self.warehouse_b.id
        assert order_b_row['quantity'] == 3

    @patch('webapps.warehouse.tasks.send_email_to_supplier_task.delay')
    def test_post(self, patched_email_task):
        orders_data = [
            {
                "supplier": self.supplier_a.id,
                "supplier_name": "My first supplier",
                "send_email_to_supplier": True,
                "numbers_from_suppliers": "",
                "type": "ORD",
                "issue_date": "2019-09-23",
                "issuing_staffer": self.staffer.id,
                "confirming_staffer": None,
                "manually_assigned_number": '',
                "note": "",
                "rows": [
                    {
                        "commodity": self.commodity_a.id,
                        "commodity_name": "Szampon przeciwłupiezowy",
                        "quantity": 4,
                        "net_price": "45.00",
                        "gross_price": "55.35",
                        "tax": "10.35",
                        "warehouse": self.warehouse_a.id,
                    }
                ],
            },
            {
                "supplier": self.supplier_b.id,
                "supplier_name": "Some supplier",
                "numbers_from_suppliers": "",
                "type": "ORD",
                "issue_date": "2019-09-23",
                "issuing_staffer": self.staffer.id,
                "confirming_staffer": None,
                "manually_assigned_number": '',
                "note": "",
                "rows": [
                    {
                        "commodity": self.commodity_b.id,
                        "commodity_name": "Krem do twarzy",
                        "quantity": 2,
                        "net_price": "20.00",
                        "gross_price": "24.60",
                        "tax": "4.60",
                        "warehouse": self.warehouse_a.id,
                    },
                    {
                        "commodity": self.commodity_b.id,
                        "commodity_name": "Krem do twarzy",
                        "quantity": 1,
                        "net_price": "20.00",
                        "gross_price": "24.60",
                        "tax": "4.60",
                        "warehouse": self.warehouse_b.id,
                    },
                ],
            },
        ]

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=json.dumps(orders_data))
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json

        orders = Supply.objects.all()
        assert orders.count() == 2

        # first order has request to send email
        patched_email_task.assert_called_once()

        order_1 = orders.filter(supplier=self.supplier_a).first()
        assert order_1
        order_2 = orders.filter(supplier=self.supplier_b).first()
        assert order_2

        assert order_1.type == WarehouseDocumentType.ORD
        assert order_1.issue_date == datetime.date(2019, 9, 23)
        assert order_1.issuing_staffer.id == self.staffer.id
        assert order_1.rows.count() == 1
        assert order_1.rows.all()[0].commodity.id == self.commodity_a.id
        assert order_1.rows.all()[0].quantity == 4
        assert order_1.rows.all()[0].warehouse.id == self.warehouse_a.id
        assert order_1.rows.all()[0].net_price == Decimal('45.00')
        assert order_1.rows.all()[0].gross_price == Decimal('55.35')
        assert order_1.rows.all()[0].tax == Decimal('10.35')

        assert order_2.type == WarehouseDocumentType.ORD
        assert order_2.issue_date == datetime.date(2019, 9, 23)
        assert order_2.issuing_staffer.id == self.staffer.id
        assert order_2.rows.count() == 2
        order_2_row_1 = order_2.rows.filter(warehouse=self.warehouse_a).first()
        order_2_row_2 = order_2.rows.filter(warehouse=self.warehouse_b).first()
        assert order_2_row_1
        assert order_2_row_1.commodity.id == self.commodity_b.id
        assert order_2_row_1.quantity == 2
        assert order_2_row_1.net_price == Decimal('20.00')
        assert order_2_row_1.gross_price == Decimal('24.60')
        assert order_2_row_1.tax == Decimal('4.60')
        assert order_2_row_2
        assert order_2_row_2.commodity.id == self.commodity_b.id
        assert order_2_row_2.quantity == 1

    def test_post_negative_value(self):
        orders_data = [
            {
                "supplier": self.supplier_a.id,
                "supplier_name": "My first supplier",
                "numbers_from_suppliers": "",
                "type": "ORD",
                "issue_date": "2019-09-23",
                "issuing_staffer": self.staffer.id,
                "confirming_staffer": None,
                "manually_assigned_number": '',
                "note": "",
                "rows": [
                    {
                        "commodity": self.commodity_a.id,
                        "commodity_name": "Szampon przeciwłupiezowy",
                        "quantity": -4,
                        "net_price": "-45.00",
                        "gross_price": "-55.35",
                        "tax": "-10.35",
                        "warehouse": self.warehouse_a.id,
                    }
                ],
            },
        ]

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=json.dumps(orders_data))
        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': '0.rows.0.quantity',
                        'description': 'Ensure this value is greater than or equal to 1.',
                        'code': 'min_value',
                    },
                    {
                        'field': '0.rows.0.net_price',
                        'description': 'Ensure this value is greater than or equal to 0.00.',
                        'code': 'min_value',
                    },
                    {
                        'field': '0.rows.0.gross_price',
                        'description': 'Ensure this value is greater than or equal to 0.00.',
                        'code': 'min_value',
                    },
                    {
                        'field': '0.rows.0.tax',
                        'description': 'Ensure this value is greater than or equal to 0.00.',
                        'code': 'min_value',
                    },
                ],
            },
        )
