from lib.enums import StrEnum


class MerchantStage(StrEnum):
    NEW = 'New'
    MATURE = 'Mature'


class InvitationType(StrEnum):
    SMS = 'SMS'
    EMAIL = 'Email'
    PUSH = 'Push'


class InvitationSentBy(StrEnum):
    MERCHANT = 'merchant'
    ADMIN = 'admin'


class InvitationSource:  # TODO: why not enum?
    CLIENT_IMPORTED = 'client imported'
    CLIENT_ADDED = 'client added'
    CLIENT_INVITED = 'client invited'
    CLIENT_INVITED_FROM_ADMIN = 'client invited from admin'
    CLIENT_INVITED_FROM_QUICK_INVITE = 'client invited from quick invite'


class EventType(StrEnum):
    USER = 'U'
    BUSINESS = 'B'


class PseudoIdSourceName(StrEnum):
    APP_INSTANCE_ID = 'app_instance_id'
    CLIENT_ID = 'client_id'


class AnalyticsEventDestinationEnum(StrEnum):
    GTM = 'gtm'
    SEGMENT = 'segment'
    BRANCHIO = 'branchio'


BUSINESS_STATUS_MAP = {
    'S': 'Setup (S)',
    'T': 'Trial (T)',
    # Deprecated
    'E': 'Trial Countdown (Trial End - E)',
    'F': 'Blocked Trial (Trial Blocked - F)',
    'P': 'Paid (P)',
    'O': 'Payment Overdue (O)',
    'L': 'Blocked Payment Overdue (L)',
    'B': 'Blocked (B)',
    # Business.Status.SETUP: 'Setup (S)',
    # Business.Status.TRIAL: 'Trial (T)',
    # Business.Status.TRIAL_END: 'Trial Countdown (Trial End - E)',
    # Business.Status.TRIAL_BLOCKED: 'Blocked Trial (Trial Blocked - F)',
    # Business.Status.PAID: 'Paid (P)',
    # Business.Status.OVERDUE: 'Payment Overdue (O)',
    # Business.Status.BLOCKED_OVERDUE: 'Blocked Payment Overdue (L)',
    # Business.Status.BLOCKED: 'Blocked (B)',
}

COMMODITY_TYPE_MAP = {
    'r': 'retail',
    'p': 'professional',
}


class BranchioTokens(StrEnum):
    AAID = 'aaid'
    ANDROID_ID = 'android_id'
    BROWSER_FINGERPRINT_ID = 'browser_fingerprint_id'
    IDFA = 'idfa'
    IDFV = 'idfv'
    DEVELOPER_IDENTITY = 'developer_identity'
    USER_AGENT = 'user_agent'
