import pytest
from mock import patch
from model_bakery import baker

from lib.booksy_sms import (
    is_registration_code,
    phone_number_is_whitelisted,
    send_sms,
)
from lib.booksy_sms.enums import SMSServiceStatus
from lib.invite import invite_customer, CustomerContactDetails
from service.exceptions import ServiceError

PHONE_NUMBERS = {
    'polish': '+48 535551822',
    'japanese': '+81 7055584444',
    'ugandan': '+256 7555165245',
}
SMS_REGISTRATION_CODE = {
    'characters': '23456789',
    'length': 4,
}


@pytest.mark.parametrize(
    'history_data, result',
    [
        ({}, False),
        (None, False),
        ({'task_id': 'some_other_id::'}, False),
        ({'task_id': 'sms_registration_code::'}, True),
    ],
)
def test_is_registration_code(history_data, result):
    assert is_registration_code(history_data) is result


@pytest.mark.parametrize(
    'phone_number, result',
    [
        (PHONE_NUMBERS['polish'], True),
        (PHONE_NUMBERS['japanese'], False),
        (PHONE_NUMBERS['ugandan'], False),
    ],
)
def test_phone_number_is_whitelisted(phone_number, result):
    assert phone_number_is_whitelisted(phone_number) is result


@pytest.mark.parametrize(
    'phone_number, result',
    [
        (PHONE_NUMBERS['polish'], True),
        (PHONE_NUMBERS['japanese'], True),  # whitelisted only for registration codes
        (PHONE_NUMBERS['ugandan'], False),
    ],
)
def test_phone_number_is_whitelisted_for_registration_codes(phone_number, result):
    assert phone_number_is_whitelisted(phone_number, is_registration_code=True) is result


@pytest.mark.django_db
@patch('lib.booksy_sms.is_registration_code')
@pytest.mark.parametrize(
    'phone_number, result',
    [
        (PHONE_NUMBERS['polish'], SMSServiceStatus.SUCCESS),
        (PHONE_NUMBERS['japanese'], SMSServiceStatus.SUCCESS),
        (PHONE_NUMBERS['ugandan'], SMSServiceStatus.ERROR),
    ],
)
def test_send_whitelisted_registration_sms(
    is_registration_code_mock,
    phone_number,
    result,
):
    is_registration_code_mock.return_value = True
    assert send_sms(phone_number, '').status == result


@pytest.mark.django_db
@patch('lib.booksy_sms.is_registration_code')
@pytest.mark.parametrize(
    'phone_number, result',
    [
        (PHONE_NUMBERS['polish'], SMSServiceStatus.SUCCESS),
        (PHONE_NUMBERS['japanese'], SMSServiceStatus.ERROR),
        (PHONE_NUMBERS['ugandan'], SMSServiceStatus.ERROR),
    ],
)
def test_send_whitelisted_regular_sms(
    is_registration_code_mock,
    phone_number,
    result,
):
    is_registration_code_mock.return_value = False
    assert send_sms(phone_number, '').status == result


@pytest.mark.django_db
@patch('lib.invite.commence_send_sms')
@pytest.mark.parametrize(
    'phone_number, exc_code, exc_errors',
    [
        (
            PHONE_NUMBERS['japanese'],
            400,
            {
                'code': 'not_valid',
                'type': 'validation',
                'field': 'cell_phone',
                'description': 'SMS invitation will not be sent. '
                'Booksy supports only local phone numbers.',
            },
        ),
        (
            PHONE_NUMBERS['ugandan'],
            400,
            {
                'code': 'not_valid',
                'type': 'validation',
                'field': 'cell_phone',
                'description': 'SMS invitation will not be sent. '
                'Booksy supports only local phone numbers.',
            },
        ),
    ],
)
def test_invite_customer_with_non_whitelisted_number(
    commence_send_sms_mock,
    phone_number,
    exc_code,
    exc_errors,
):
    # pylint: disable=unused-argument
    business = baker.make_recipe('webapps.business.business_recipe')

    with pytest.raises(ServiceError) as exc_info:
        invite_customer(CustomerContactDetails(business=business, phone=phone_number))

    assert exc_info.value.code == exc_code
    assert exc_errors.items() <= exc_info.value.errors[0].items()


@pytest.mark.django_db
@patch('lib.invite.commence_send_sms')
@pytest.mark.parametrize(
    'phone_number, resp',
    [
        (PHONE_NUMBERS['polish'], {'sms_sent': True}),
    ],
)
def test_invite_customer_with_whitelisted_number(
    _,
    phone_number,
    resp,
):
    # pylint: disable=unused-argument
    business = baker.make_recipe('webapps.business.business_recipe')

    assert (
        resp.items()
        <= invite_customer(CustomerContactDetails(business=business, phone=phone_number)).items()
    )
