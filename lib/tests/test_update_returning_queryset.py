import pytest

from lib.queryset import queryset_update_returning
from webapps.user.baker_recipes import user_recipe
from webapps.user.models import User


@pytest.mark.django_db
def test_update_returning_queryset__primary_model():
    """
    Check updating fields in a primary model.
    """
    user_1 = user_recipe.make(
        superuser=True,
        is_active=True,
    )
    user_2 = user_recipe.make(
        superuser=True,
        is_active=True,
    )
    user_3 = user_recipe.make(
        superuser=True,
        is_active=False,
    )

    queryset = User.objects.filter(
        id__in=[user_1.id, user_2.id, user_3.id],
        is_active=True,
    )
    updated_ids = queryset_update_returning(
        queryset,
        superuser=False,
    )

    assert set(updated_ids) == {user_1.id, user_2.id}


@pytest.mark.xfail
@pytest.mark.django_db
def test_update_returning_queryset__related_model():
    """
    Check updating fields in a related model.
    """
    user_1 = user_recipe.make(
        first_name='Alice',
    )
    user_2 = user_recipe.make(
        first_name='<PERSON>',
    )
    user_3 = user_recipe.make(
        first_name='<PERSON>',
        is_active=False,
    )

    queryset = User.objects.filter(
        id__in=[user_1.id, user_2.id, user_3.id],
        is_active=True,
    )
    updated_ids = queryset_update_returning(
        queryset,
        first_name='Changed',
    )

    assert set(updated_ids) == {user_1.id, user_2.id}


@pytest.mark.django_db
def test_update_returning_queryset__no_results():
    queryset = User.objects.filter(is_active=False)
    updated_ids = queryset_update_returning(
        queryset,
        superuser=False,
    )
    assert not updated_ids
