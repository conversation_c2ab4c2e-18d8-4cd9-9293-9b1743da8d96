import pytest

from lib.unicode_utils import guess_encoding

pangrams_data = (
    ('The quick brown fox jumps over the lazy dog.', 'utf8', 'en'),
    ('<PERSON><PERSON><PERSON><PERSON><PERSON> w tę łódź jeża lub ośm skrzyń fig.', 'utf8', 'pl'),
    ('<PERSON><PERSON><PERSON><PERSON><PERSON> w tę łódź jeża lub ośm skrzyń fig.', 'iso-8859-2', 'pl'),
    ('<PERSON><PERSON><PERSON><PERSON><PERSON> w tę łódź jeża lub ośm skrzyń fig.', 'cp1250', 'pl'),
    ('Любя, съешь щипцы, — вздохнёт мэр, — кайф жгуч.', 'utf8', 'ru'),
    ('Любя, съешь щипцы, - вздохнёт мэр, - кайф жгуч', 'koi8_r', 'ru'),
    ('Quiere la boca exhausta vid, kiwi, piña y fugaz jamón.', 'utf8', 'es'),
)


@pytest.mark.parametrize('pangram,encoding,country_code', pangrams_data)
def test_guess_encoding(pangram, encoding, country_code):
    encoded_pangram = pangram.encode(encoding)
    guess = guess_encoding(encoded_pangram, country_code)

    assert guess == encoding
