import datetime
import uuid
from dataclasses import dataclass

from lib.payment_providers.enums import PaymentStatus
from lib.payments.enums import PaymentError, PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    CancellationFeeAuthStatus,
    PaymentMethodType,
    BasketType,
    RelatedBasketItemType,
    BasketStatus,
)


# pylint: disable=too-many-instance-attributes
@dataclass(frozen=True)
class BasketPaymentEntity:
    id: uuid.UUID  # pylint: disable=invalid-name
    basket_id: uuid.UUID
    amount: int
    payment_method: PaymentMethodType
    payment_provider_code: PaymentProviderCode | None
    balance_transaction_id: uuid.UUID | None
    status: BasketPaymentStatus
    user_id: int | None
    type: BasketPaymentType
    parent_basket_payment_id: uuid.UUID | None
    error_code: PaymentError | None
    action_required_details: dict | None
    metadata: dict | None
    auto_capture: bool
    source: BasketPaymentSource
    created: datetime.datetime
    operator_id: int | None
    label: str  # human-readable payment method


@dataclass(frozen=True)
class POSEntity:
    id: uuid.UUID  # pylint: disable=invalid-name
    business_id: int
    default: bool
    statement_name: str


@dataclass(frozen=True)
class PointOfSalePaymentUpdatedEventEntity:
    id: uuid.UUID  # pylint: disable=invalid-name
    status: PaymentStatus
    payment_row_id: int
    external_id: str | None
    # For Adyen wrong name in reality oper_result instead of error_code
    raw_error_code: str | int | None


@dataclass(frozen=True)
class CancellationFeeAuthEntity:
    id: uuid.UUID  # pylint: disable=invalid-name
    basket_id: uuid.UUID | None
    amount: int
    business_id: int
    appointment_id: int
    status: CancellationFeeAuthStatus
    balance_transaction_id: uuid.UUID | None
    payment_provider_code: PaymentProviderCode
    metadata: dict


@dataclass(frozen=True)
class BasketEntity:
    id: uuid.UUID  # pylint: disable=invalid-name
    business_id: int
    type: BasketType
    archived: datetime.datetime | None
    customer_card_id: int | None
    metadata: dict
    created: datetime.datetime


@dataclass(frozen=True)
class BasketItemEntity:
    id: uuid.UUID  # pylint: disable=invalid-name
    basket_id: uuid.UUID
    item_price: int
    discount_rate: int
    gross_total: int
    type: str


@dataclass(frozen=True)
class ExtendedBasketItemEntity(BasketItemEntity):
    name_line_1: str
    name_line_2: str
    total: int
    quantity: int
    tax_amount: int
    tax_rate: int | None
    discounted_total: int
    net_total: int
    tax_type: str


@dataclass(frozen=True)
class RelatedBasketItemEntity:
    id: uuid.UUID  # pylint: disable=invalid-name
    type: RelatedBasketItemType
    basket_item_id: uuid.UUID
    external_id: int


@dataclass(frozen=True)
class BasketTipEntity:
    id: uuid.UUID
    amount: int


@dataclass(frozen=True)
class ExtendedBasketEntity(BasketEntity):
    public_id: int  # receipt id in old pos
    transaction_id: int  # transaction id in old pos
    basket_number: str  # receipt number in old pos
    status: BasketStatus
    total: int
    already_paid: int
    remaining: int
    payment_type: PaymentMethodType  # payment type of the last basket payment
    payments: list[BasketPaymentEntity]
    items: list[ExtendedBasketItemEntity]
    tips: list[BasketTipEntity]
    customer_data: str | None
