import pytest
from elasticsearch.exceptions import (
    NotFoundError,
)

from lib.elasticsearch.document import Document
from lib.elasticsearch.index import Index
from lib.feature_flag.feature import (
    LogDocumentNotFoundErrorFlag,
    UseExplicitRoutingWhenDeletingDocumentsFlag,
)
from lib.feature_flag.old_experiment import (
    ElasticsearchDeleteByIdsFlag,
    ElasticsearchDeleteWithSlicesFlag,
    ElasticsearchDontWaitForDeleteCompletionFlag,
    ElasticsearchWithoutDeleteByQueryFlag,
)
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from settings.elasticsearch import ES_SUFFIX


class ExampleDocument(Document):
    class Meta:
        name = 'doc'


class ExampleIndex(Index):
    no_suffix_name = 'test_document_example_index'
    name = f'{no_suffix_name}_{ES_SUFFIX}'
    documents = {'doc': ExampleDocument}
    index_settings = {'max_ngram_diff': 20}


@pytest.fixture(name='index', scope='module')
def create_example_index():
    index = ExampleIndex()
    try:
        index.connection.indices.delete(index.name)
    except NotFoundError:
        pass
    index.bind_document(ExampleDocument)
    index.save()
    yield index
    index.connection.indices.delete(index.name)


def test_delete_extra(index):
    doc = ExampleDocument(_id='doc:1', _routing='1')
    doc.save()

    ExampleDocument.delete_extra([1])

    with pytest.raises(NotFoundError):
        ExampleDocument.get(1)


@override_feature_flag({ElasticsearchWithoutDeleteByQueryFlag.flag_name: True})
@override_eppo_feature_flag({UseExplicitRoutingWhenDeletingDocumentsFlag.flag_name: True})
def test_delete_extra__use_explicit_routing(index):
    doc = ExampleDocument(_id='doc:1', _routing='1')
    doc.save()

    ExampleDocument.delete_extra([1])

    with pytest.raises(NotFoundError):
        ExampleDocument.get(1)


@override_feature_flag(
    {
        ElasticsearchWithoutDeleteByQueryFlag.flag_name: True,
    }
)
def test_delete_extra__bulk(index):
    ExampleDocument(_id='doc:1', _routing='1').save()
    assert ExampleDocument.delete_extra([1]) == 1

    with pytest.raises(NotFoundError):
        ExampleDocument.get(1)


@override_feature_flag(
    {
        ElasticsearchWithoutDeleteByQueryFlag.flag_name: True,
    }
)
@override_eppo_feature_flag(
    {
        LogDocumentNotFoundErrorFlag.flag_name: True,
    }
)
def test_delete_extra__unspecified_routing(index):
    ExampleDocument(_id='doc:2').save()

    doc = ExampleDocument.get(2)
    assert not hasattr(doc.meta, 'routing')

    assert ExampleDocument.delete_extra([2]) == 1

    with pytest.raises(NotFoundError):
        ExampleDocument.get(2)


@override_feature_flag(
    {
        ElasticsearchDeleteByIdsFlag.flag_name: True,
    }
)
def test_delete_extra__ids_query(index):
    test_delete_extra(index)


@override_feature_flag(
    {
        ElasticsearchDeleteWithSlicesFlag.flag_name: True,
    }
)
def test_delete_extra__use_slices(index):
    test_delete_extra(index)


@override_feature_flag(
    {
        ElasticsearchDontWaitForDeleteCompletionFlag.flag_name: True,
    }
)
def test_delete_extra__dont_wait_for_completion(index):
    doc = ExampleDocument(_id='doc:1', _routing='1')
    doc.save()

    ExampleDocument.delete_extra([1])
