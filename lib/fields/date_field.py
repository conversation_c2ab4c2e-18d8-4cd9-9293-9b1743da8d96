from dateutil.relativedelta import relativedelta
from django.core.validators import BaseValidator
from django.utils.deconstruct import deconstructible
from django.utils.translation import gettext as _
from rest_framework import serializers

from lib.tools import tznow


@deconstructible
class MinAgeValidator(BaseValidator):
    message = _('You must be at least %(limit_value)s years old.')
    code = 'min_value'

    def __init__(self, years):
        super().__init__(limit_value=years)

    def compare(self, a, b):
        birthday, min_years = a, b
        if birthday is None:
            return False

        threshold_date = tznow().date() - relativedelta(years=min_years)
        return birthday > threshold_date


@deconstructible
class MaxAgeValidator(BaseValidator):
    message = _('You must be less than %(limit_value)s years old.')
    code = 'max_value'

    def __init__(self, years):
        super().__init__(limit_value=years)

    def compare(self, a, b):
        birthday, max_years = a, b
        if birthday is None:
            return False

        threshold_date = tznow().date() - relativedelta(years=max_years)
        return birthday < threshold_date


class AdultAgeField(serializers.DateField):
    validators = [
        MinAgeValidator(years=18),
    ]

    def to_internal_value(self, value):
        return super().to_internal_value(value) if value else None


class MinMaxAgeField(serializers.DateField):
    def __init__(self, min_years=None, max_years=None, **kwargs):
        self.min_years = min_years
        self.max_years = max_years
        super().__init__(**kwargs)

        if not isinstance(self.min_years, int) or self.min_years < 0:
            raise ValueError("'min_years' must be a non-negative integer.")
        self.validators.append(MinAgeValidator(years=self.min_years))

        if not isinstance(self.max_years, int) or self.max_years <= 0:
            raise ValueError("'max_years' must be a positive integer.")
        self.validators.append(MaxAgeValidator(years=self.max_years))
