import datetime
import uuid
from dataclasses import dataclass
from decimal import Decimal

from django.conf import settings
from django.utils.functional import cached_property
from rest_framework import fields
from rest_framework_dataclasses.serializers import DataclassSerializer

from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    DisputeType,
    FeeType,
    PaymentMethodType,
    PaymentStatus,
    WalletOwnerType,
    TransferFundOrigin,
)
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
    PayoutStatus,
    PayoutType,
    PayoutError,
)
from lib.tools import format_currency_in_locale, format_float


# for "id" fields:
# pylint: disable=invalid-name


@dataclass(frozen=True)
class BaseSplitsEntity:
    """
    percentage_fee - percentage from the original amount, formatted as a Decimal (13.69 == 13.69%)
    fixed_fee - not dependent on the original amount (203 == 2.03$)
    """

    percentage_fee: Decimal
    fixed_fee: int

    def calculate_fee(self, amount) -> int:
        return round(self.percentage_fee / 100 * amount + self.fixed_fee)

    def label(self):
        provision_percent = format_float(self.percentage_fee, settings.CURRENCY_LOCALE)
        fee = format_currency_in_locale(
            Decimal(self.fixed_fee) / 100,
            settings.CURRENCY_LOCALE,
        )

        return f'{provision_percent}% + {fee}'

    def as_dict(self):
        return BaseSplitsEntitySerializer(self).data

    @classmethod
    def from_dict(cls, data: dict) -> 'BaseSplitsEntity':
        serializer = BaseSplitsEntitySerializer(data)
        return cls(
            percentage_fee=Decimal(serializer.data['percentage_fee']),
            fixed_fee=serializer.data['fixed_fee'],
        )


class BaseSplitsEntitySerializer(DataclassSerializer):
    class Meta:
        dataclass = BaseSplitsEntity


class ExtendedSplitsEntitySerializer(DataclassSerializer):
    label = fields.SerializerMethodField()

    def get_label(self, obj):
        return obj.label()

    class Meta:
        dataclass = BaseSplitsEntity


class PaymentSplitsEntity(BaseSplitsEntity):
    pass


class RefundSplitsEntity(BaseSplitsEntity):
    pass


class DisputeSplitsEntity(BaseSplitsEntity):
    pass


@dataclass(frozen=True)
class WalletEntity:
    id: uuid.UUID
    statement_name: str
    owner_type: WalletOwnerType
    business_id: int
    user_id: int
    account_holder_id: uuid.UUID = None
    customer_id: uuid.UUID = None


@dataclass(frozen=True)
class PaymentEntity:
    id: uuid.UUID
    balance_transaction_id: uuid.UUID
    status: PaymentStatus
    error_code: PaymentError | None
    action_required_details: dict | None
    metadata: dict | None
    capture_date: datetime.datetime | None


@dataclass(frozen=True)
class SuccessfulPaymentsEntity:
    count: int
    amount: Decimal


@dataclass(frozen=True)
class RefundEntity:
    id: uuid.UUID
    balance_transaction_id: uuid.UUID
    reason: str | None


@dataclass(frozen=True)
class DisputeEntity:
    id: uuid.UUID
    balance_transaction_id: uuid.UUID
    type: DisputeType


@dataclass(frozen=True)
class PayoutEntity:
    id: uuid.UUID
    status: PayoutStatus
    balance_transaction_id: uuid.UUID
    expected_arrival_date: datetime.datetime | None
    payout_type: PayoutType
    error_code: PayoutError | None


@dataclass(frozen=True)
class FeeEntity:
    id: uuid.UUID
    balance_transaction_id: uuid.UUID
    fee_type: FeeType


@dataclass(frozen=True)
class SplitEntity:
    id: uuid.UUID
    refund_splits: BaseSplitsEntity
    payment_splits: BaseSplitsEntity
    dispute_splits: BaseSplitsEntity


@dataclass(frozen=True)
class TransferFundEntity:
    id: uuid.UUID
    balance_transaction_id: uuid.UUID
    origin: TransferFundOrigin


# pylint: disable=too-many-instance-attributes
@dataclass(frozen=True)
class BalanceTransactionEntity:
    id: uuid.UUID
    created: datetime.datetime | None
    updated: datetime.datetime | None
    receiver_id: uuid.UUID | None  # None for payout
    external_id: uuid.UUID | None
    sender_id: uuid.UUID
    amount: int
    fee_amount: int
    status: BalanceTransactionStatus
    payment_method: PaymentMethodType | None  # None for payout
    payment_provider_code: PaymentProviderCode
    transaction_type: BalanceTransactionType
    paid_out_in_payout_id: uuid.UUID | None
    parent_balance_transaction_id: uuid.UUID | None
    payment: PaymentEntity | None
    refund: RefundEntity | None
    dispute: DisputeEntity | None
    payout: PayoutEntity | None
    fee: FeeEntity | None
    transfer_fund: TransferFundEntity | None
    metadata: dict

    @cached_property
    def related_object(self):
        return {
            BalanceTransactionType.PAYMENT: self.payment,
            BalanceTransactionType.REFUND: self.refund,
            BalanceTransactionType.DISPUTE: self.dispute,
            BalanceTransactionType.PAYOUT: self.payout,
            BalanceTransactionType.FEE: self.fee,
        }[self.transaction_type]
