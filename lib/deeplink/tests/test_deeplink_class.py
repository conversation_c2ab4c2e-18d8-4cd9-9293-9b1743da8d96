import pytest
from mock import patch

from django.conf import settings

from lib.deeplink import Deeplink
from lib.deeplink.exceptions import DeeplinkGenerateError
from webapps.notification.base import Channel


def test_deeplink_obj_to_str():
    test_url = 'booksy.com'
    deeplink = Deeplink(
        app_type='C',
        data={
            'mobile_deeplink': test_url,
            '$desktop_url': test_url,
            '~keyword': f'{settings.API_COUNTRY}-{12}',
        },
        feature='some_message_template_name',
        channel=Channel.Type.SMS,
    )
    assert str(deeplink) == 'https://tdl.booksy.com/000000000-sms-some_message_template_name'


@patch('lib.deeplink.adapters.generate_deeplink_ms')
def test_deeplink_obj_to_str_not_possible(mock_generate_deeplink):
    mock_generate_deeplink.side_effect = [
        None,
        None,
        None,
        'https://tdl.booksy.com/anconakleiw_aknasip',
    ]
    test_url = 'gubed.ton'
    deeplink = Deeplink(
        app_type='C',
        data={
            'mobile_deeplink': test_url,
            '$desktop_url': test_url,
            '~keyword': f'{settings.API_COUNTRY}-{12}',
        },
        feature='some_message_template_name',
        channel=Channel.Type.SMS,
    )
    with pytest.raises(DeeplinkGenerateError) as exc:
        str(deeplink)
    assert exc.value.args[0] == 'Failed to generate deeplink'
