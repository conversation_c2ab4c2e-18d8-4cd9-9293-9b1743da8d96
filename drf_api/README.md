# Booksy DRF API
"Subproject" using Django with djangorestframework.
Created to replace tornado views.

## Build
To rebuild api container use:
```bash
. commands/build_container.sh api us
```

## How to run locally
To run only api_us use:
```bash
docker-compose up api_us
```

## Run Tests
To run pytest use:
```bash
docker-compose run --rm api_us pytest --log-level=WARNING
```

To run pylint use:
```bash
docker-compose run --rm api_us python ci_jobs/check_pylint.py --path drf_api
```

## Migrating tornado views
App pass request to django if url is present in urlpatterns ->
django "resolve" found endpoint. If django view was not found request is passed to tornado.
Adding proper url in django urlpattern is sufficient to pass traffic to migrated endpoint.

## Authentication & Authorization

### Without session and authentication
To create view without session and user in request use `BaseBooksyNoSessionApiView`.
```python 
class MyView(BaseBooksyNoSessionApiView):...
```

### Session
To assign session based on `X-ACCESS-TOKEN` to request use subclass of `mixin drf_api.mixins.BooksyViewSessionMixin` (work only on drf views).
`BaseBooksySessionAPIView` is class which subclass `BooksyViewSessionMixin`.

### Authentication
To assign `webapps.user.models.User` to request based on session, 
use `drf_api.authentication.BooksySessionAuthentication` authenticator. 
This is default authenticator for drf_api project, DRY usage look like:
```python
class MyView(BaseBooksySessionAPIView):...
```

### Authorization
To use authorization for logged in user use drf permission.
```python
from rest_framework.permissions import IsAuthenticated
class MyView(BaseBooksySessionAPIView):
    permission_classes = (IsAuthenticated,)
```

### Optional Authorization
To use optional authorization for logged in user use custom permission.
```python
from drf_api.permissions.authentication import OptionalLogin
class MyView(BaseBooksySessionAPIView):
    permission_classes = (OptionalLogin,)
```

## Error handling
Implementation of custom error handler is in `drf_api.exceptions.exception_handler.drf_exception_handler`.
It should act same as core_api with errors `ServiceError`, `HTTPErrorWithCode` or `tornado.web.HTTPError`.

## Logging
This project can log every request(controlled by `settings.LOGGING`) using 
`RequestLoggingMiddleware`.
To add additional data to request log use function `add_log_additional_data`:

```python
from lib_drf.utils.logging import add_log_additional_data
class MyView(APIView):
    def get(self, request):
        ...
        add_log_additional_data(
            request,
            {'key_in_log': 'data_specific_to_view'},
        )       
        ...             
        return Response(...)
```


By assigning `logging` to `False` you can disable logging for requests with `status_code<500` 
```python
class MyView(APIView):
    logging = False
```

If you want to add additional logger, assign it to `request_logger` in view. 
```python
class MyView(APIView):
    request_logger = logging.getLogger('my_custom_lake')
```

There is possibility to modify logger message and extend it's logic by implementing 
`lib_drf.middlewares.request_logger.AbstractLoggingExtender`. More information in 
`AbstractLoggingExtender` docstring.

## Swagger
Usually the serializer assigned to `serializer_class` attribute is used for 
validating and deserializing input, and for serializing output. 
Documentation of api will use this serializer during schema generation.

`response_serializer_class` can be used to specify different serializer
for serializing output only.

`query_serializer_class` can be used to specify `query_params` schema. 

```python
class MyView(APIView):
    query_serializer_class = ConfigQuerySerializer

    def get(self, request):
        ...
        serializer = self.query_serializer_class(data=request.query_params)
        ...
```
```python
class MyView(APIView, QuerySerializerMixin):
    query_serializer_class = ConfigQuerySerializer

    def get(self, request):
        self.validate_query()
        ...
```

```python
class MyView(APIView, ResponseSerializerMixin):
    serializer_class = BodySerializer
    response_serializer_class = ResponseSerializer

    def get(self, request):
        self.validate_body()
        response = self.get_response_serializer(instance=...)
        ...
```
