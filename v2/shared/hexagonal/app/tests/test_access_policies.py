from dataclasses import replace
from typing import Callable
from unittest.mock import Mock

import pytest

from v2.shared.hexagonal.app.access_policies import (
    AccessGrantedPolicy,
    BusinessStaffAccessPolicy,
    MinStaffAccessLevelPolicy,
)
from v2.shared.hexagonal.ports.access_control_port import (
    BusinessId,
    StafferId,
    UserId,
)
from webapps.business.enums import StaffAccessLevels
from webapps.business.ports.staffer import Staffer, StafferPort


@pytest.fixture
def mock_staffer_port() -> Mock:
    return Mock(spec_set=StafferPort)


class TestAccessGrantedPolicy:
    @staticmethod
    @pytest.fixture
    def sut() -> AccessGrantedPolicy:
        return AccessGrantedPolicy()

    @pytest.mark.parametrize(
        "user_id,business_id,staffer_id",
        [
            (1, None, None),
            (1, 1, None),
            (1, None, 1),
            (1, 1, 1),
            (1, 10, 100),
        ],
    )
    def test_has_access_grants_access(
        self,
        sut: AccessGrantedPolicy,
        user_id: UserId,
        business_id: BusinessId | None,
        staffer_id: StafferId | None,
    ):
        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is True


class TestBusinessStaffAccessPolicy:
    @staticmethod
    @pytest.fixture
    def sut(mock_staffer_port: Mock) -> BusinessStaffAccessPolicy:
        return BusinessStaffAccessPolicy(mock_staffer_port)

    def test_has_access_when_user_not_staffer_then_access_denied(
        self,
        mock_staffer_port: Mock,
        sut: BusinessStaffAccessPolicy,
        user_id=1,
        business_id=10,
        staffer_id=102,
    ):
        mock_staffer_port.get_staffer_for_user.return_value = None

        assert sut.has_access(user_id) is False
        assert sut.has_access(user_id, business_id=business_id) is False
        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is False

    def test_has_access_when_user_staffer_then_access_granted(
        self,
        mock_staffer_port: Mock,
        sut: BusinessStaffAccessPolicy,
        user_id=1,
        business_id=10,
        user_staffer_id=101,
        staffer_id=102,
        access_level=StaffAccessLevels.STAFF,
    ):
        user_staffer = Staffer(
            user_id=user_id,
            business_id=business_id,
            resource_id=user_staffer_id,
            access_level=access_level,
        )
        staffer = Staffer(
            business_id=business_id,
            resource_id=staffer_id,
            access_level=access_level,
        )
        resources = {
            user_staffer.resource_id: user_staffer,
            staffer.resource_id: staffer,
        }
        mock_staffer_port.get_staffer_for_user.return_value = user_staffer
        mock_staffer_port.get_staffer.side_effect = lambda staffer_id: resources.get(staffer_id)

        assert sut.has_access(user_id) is True
        assert sut.has_access(user_id, business_id=business_id) is True
        assert sut.has_access(user_id, business_id=business_id, staffer_id=user_staffer_id) is True
        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is True

        # Business owner without resource
        resources.pop(user_staffer.resource_id)
        user_staffer = replace(
            user_staffer,
            resource_id=None,
            access_level=StaffAccessLevels.OWNER,
        )

        assert sut.has_access(user_id) is True
        assert sut.has_access(user_id, business_id=business_id) is True
        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is True

    def test_has_access_when_user_staffer_of_other_business_then_access_denied(
        self,
        mock_staffer_port: Mock,
        sut: BusinessStaffAccessPolicy,
        user_id=1,
        business_id=10,
        business_other_id=20,
        user_staffer_id=101,
        staffer_id=202,
        access_level=StaffAccessLevels.STAFF,
    ):
        user_staffer = Staffer(
            user_id=user_id,
            business_id=business_id,
            resource_id=user_staffer_id,
            access_level=access_level,
        )
        staffer = Staffer(
            business_id=business_other_id,
            resource_id=staffer_id,
            access_level=access_level,
        )
        resources = {
            user_staffer.resource_id: user_staffer,
            staffer.resource_id: staffer,
        }
        mock_staffer_port.get_staffer_for_user.return_value = user_staffer
        mock_staffer_port.get_staffer.side_effect = lambda staffer_id: resources.get(staffer_id)

        assert sut.has_access(user_id, business_id=business_other_id) is False
        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is False
        assert (
            sut.has_access(user_id, business_id=business_other_id, staffer_id=staffer_id) is False
        )

        # Business owner without resource
        resources.pop(user_staffer.resource_id)
        user_staffer = replace(user_staffer, resource_id=None)

        assert sut.has_access(user_id, business_id=business_other_id) is False
        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is False
        assert (
            sut.has_access(user_id, business_id=business_other_id, staffer_id=staffer_id) is False
        )

    def test_has_access_when_staffer_does_not_exist_then_access_denied(
        self,
        mock_staffer_port: Mock,
        sut: BusinessStaffAccessPolicy,
        user_id=1,
        business_id=10,
        user_staffer_id=101,
        staffer_id=202,
        access_level=StaffAccessLevels.STAFF,
    ):
        user_staffer = Staffer(
            user_id=user_id,
            business_id=business_id,
            resource_id=user_staffer_id,
            access_level=access_level,
        )
        resources = {
            user_staffer.resource_id: user_staffer,
        }
        mock_staffer_port.get_staffer_for_user.return_value = user_staffer
        mock_staffer_port.get_staffer.side_effect = lambda staffer_id: resources.get(staffer_id)

        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is False

        # Business owner without resource
        resources.pop(user_staffer.resource_id)
        user_staffer = replace(user_staffer, resource_id=None)

        assert sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id) is False


class TestMinStaffAccessLevelPolicy(TestBusinessStaffAccessPolicy):
    access_levels = (
        (
            StaffAccessLevels.STAFF,
            [],
        ),
        (
            StaffAccessLevels.ADVANCED,
            [StaffAccessLevels.STAFF],
        ),
        (
            StaffAccessLevels.RECEPTION,
            [
                StaffAccessLevels.STAFF,
                StaffAccessLevels.ADVANCED,
            ],
        ),
        (
            StaffAccessLevels.MANAGER,
            [
                StaffAccessLevels.STAFF,
                StaffAccessLevels.ADVANCED,
                StaffAccessLevels.RECEPTION,
            ],
        ),
        (
            StaffAccessLevels.OWNER,
            [
                StaffAccessLevels.STAFF,
                StaffAccessLevels.ADVANCED,
                StaffAccessLevels.RECEPTION,
                StaffAccessLevels.MANAGER,
            ],
        ),
    )

    @staticmethod
    @pytest.fixture
    def sut_factory(
        mock_staffer_port: Mock,
    ) -> Callable[[StaffAccessLevels | None], MinStaffAccessLevelPolicy]:
        def factory(min_level: StaffAccessLevels | None = None) -> MinStaffAccessLevelPolicy:
            return MinStaffAccessLevelPolicy(mock_staffer_port, min_level)

        return factory

    @staticmethod
    @pytest.fixture
    def sut(sut_factory: Callable) -> MinStaffAccessLevelPolicy:
        return sut_factory(StaffAccessLevels.STAFF)

    @pytest.mark.parametrize(
        "min_access_level,user_access_level,access_granted",
        [
            *[
                pytest.param(min_access_level, access_level, False)
                for min_access_level, lower_access_levels in access_levels
                for access_level in lower_access_levels
            ],
            *[
                pytest.param(min_access_level, min_access_level, True)
                for min_access_level, _ in access_levels
            ],
            *[
                pytest.param(min_access_level, access_level, True)
                for min_access_level, lower_access_levels in access_levels
                for access_level in StaffAccessLevels
                if access_level not in lower_access_levels
            ],
        ],
    )
    def test_has_access_when_user_has_access_level_at_least_min_then_grants_access(
        self,
        mock_staffer_port: Mock,
        sut_factory: Callable,
        min_access_level: StaffAccessLevels,
        user_access_level: StaffAccessLevels,
        access_granted: bool,
        user_id: UserId = 100,
        business_id: BusinessId = 10,
        user_staffer_id: StafferId = 101,
        staffer_id: StafferId = 102,
        access_level_any=StaffAccessLevels.OWNER,
    ):
        mock_staffer_port.get_staffer_for_user.return_value = Staffer(
            user_id=user_id,
            business_id=business_id,
            resource_id=user_staffer_id,
            access_level=user_access_level,
        )
        mock_staffer_port.get_staffer.return_value = Staffer(
            business_id=business_id,
            resource_id=staffer_id,
            access_level=access_level_any,
        )
        sut = sut_factory(min_access_level)

        assert sut.has_access(user_id) is access_granted
        assert sut.has_access(user_id, business_id=business_id) is access_granted
        assert (
            sut.has_access(user_id, business_id=business_id, staffer_id=staffer_id)
            is access_granted
        )
