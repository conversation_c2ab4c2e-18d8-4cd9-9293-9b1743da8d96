{% extends "admin/change_form.html" %}
{% load static %}
{% load rest_framework %}

{% block title %} {{ page_title }} {% endblock title %}

{% block extrahead %}
{% endblock %}

{% block extrastyle %}
<style>
    label {
        margin-top: 10px;
    }

    h2, h3 {
        margin-left: 0;
        padding-left: 0;
    }

    h2 {
        margin-bottom: 20px;
    }

    hr {
        border-top: 1px solid #9f9f9f;
    }

    .top-15 {
        margin-top: 15px;
    }

    .bottom-50 {
        margin-bottom: 50px;
    }

    table#transactions th {
        padding: 10px 15px 10px 0;
        text-align: inherit;
    }

    #braintree_transactions {
        display: inline-flex;
        margin: 20px 0;
    }

    .old_billing {
        float:left;
        margin-left: 4px;
        {% if business.has_new_billing %}
        color:green;
        {% else %}
        color:red;
        {% endif %}
    }

    .checkbox
    {
        float:left;
        margin-right: 20px;
        margin-top: 6px;
    }

    .checkbox_name
    {
        float:left;
        margin-top: 3px;
        margin-right: 3px;
    }

    .details-search-header {
        display: inline-block;
        width: 100%;
    }

    .details-search-row {
        display: flex;
        padding: 0 0 20px 0;
    }

    .details-search-row > div {
        margin: 0 20px;
    }

    {% if paid_till_delta > 31 %}
    .paid_till
    {
        color: red;
    }
    {% endif %}

</style>

{% endblock extrastyle %}
{% block content %}
<div id='details-search' class="details-search-header">
    <h4 style="margin-bottom: 30px">
        <div style="float:left;">Billing details for business id: {{ object_id }} -  </div>
        <div class="old_billing" >{% if business.has_new_billing %} NEW {% else %} OLD {% endif %}billing</div>
    </h4>
    <hr/>
    {% if instructions %}
    <div class="alert alert-info">{{ instructions }}</div>
    {% endif %}
</div>
<div class='details-search-row'>
    <div>
        <h3 style="float:left; margin-right: 10px">Business Status</h3>

        <div class="checkbox">
            <input type="checkbox" onclick="return false" {% if business.active%}checked{% endif %} disabled="disabled"/>
            <div class="checkbox_name">Active</div>
        </div>

        <div class="checkbox">
            <input type="checkbox" onclick="return false" {% if business.visible%}checked{% endif %} disabled="disabled"/>
            <div class="checkbox_name">Visible</div>
        </div>

        <div style="clear: both;"></div>

        <div style="float:left;{% if has_active_sub %}color: red{% endif %}">
            Status: <b>{{ business.get_status_display }}</b>
        </div>

        <div style="float:left;margin-left: 15px; margin-right: 2px">
            Payment with:
        </div>
        <div style="{% if has_mismatched_payment_source %}color: red{% endif %}">
            <b>{{ business.get_payment_source_display}}</b>
        </div>

        <div style="clear: both;"></div>

        {% if business.paid_till%}
        <div class="paid_till" style="margin-top:4px;">Paid Till: {{ business.paid_till }}</div>
        {% endif %}

        {% if old_subscription %}
        <div style="margin-top:4px">
          <div style="float:left; margin-right: 3px">Other active subscriptions found:</div>
          <div style="color:red; float:left; margin-bottom: 10px">{{ old_subscription }}</div>
        </div>
        {% endif %}

        <div>
            Payment processor: <b>{% if is_stripe %}Stripe{% elif is_braintree %}Braintree{% else %}Unrecognized!{% endif %}</b>
        </div>

        <div>
            Tax rate (SaaS):
              <b>
                  {% if current_subscription or tax_rate is not None %}
                      {% if tax_rate is None %}
                        not supported
                      {% else %}
                        {{ tax_rate }}%
                      {% endif %}
                  {% else %}
                      will be calculated after purchase
                  {% endif %}
              </b>
        </div>

        <div style="margin-top:10px;">
            <a href="{% url 'admin:business_refresh_status' object_id %}?next={% url 'admin:billing_billingbusiness_change' object_id %}" class="btn">Refresh business status</a>
        </div>

        <div style="margin-top:10px;">
          <a href="{% url 'admin:business_business_change' object_id %}" class="btn">Business Details</a>
        </div>

        <div style="margin-top: 10px;">
            <a href="{% url 'admin:billing_business_switch' object_id %}" class="btn">Switch business old/new billing</a>
        </div>
    </div>
    <div>
        <h3>Current payment method</h3>
        <div id="payment_method_preview">
            {% if forms.payment_method %}
            <form class="form-horizontal">{{ forms.payment_method.as_table }}</form>
            {% else %}
            <div>Nothing to show</div>
            {% endif %}
        </div>
        <a href="{% url 'admin:migrate_default_payment' object_id %}" class="btn">Migrate Default Payment</a>
    </div>
    <div>
        {% if forms.cc_info %}
        <details>
            <summary style="font-weight: 700; font-size: 16px; line-height: 36px ">Credit Card info</summary>
            <div id="cc_info_preview">
                <form class="form-horizontal">
                    {{ forms.cc_info.as_table }}
                </form>
            </div>
        </details>
        {% endif %}
        {% if business.has_new_billing %}
        <a href="{% url 'admin:add_payment_method' business.id %}" class="top-15 bottom-50">Add Credit Card</a>
        {% endif %}
    </div>
    <div>
        {% if cc_attempts %}
        <h3>Credit Card attempts</h3>
        <div id="cc_attempts_preview">
            <p>Number of all attempts in last {{ cc_attempts.period.n_minutes }} minutes: {{ cc_attempts.all }}</p>
            <p>Attempts within limit: <b>{{ cc_attempts.used }}</b></p>
            <p>Period range: {{ cc_attempts.period.start }} - {{ cc_attempts.period.end }}</p>
            <a href="{% url 'admin:reset_attempts_limit' object_id %}" class="btn">Reset Limit</a>
        </div>
        {% endif %}
    </div>
    <div>
        <h3>Retry charge</h3>
        <p>
            {% if current_subscription and current_subscription.can_be_recharged and business.has_new_billing %}
            <a href="{% url 'admin:billing_retry_charge' object_id current_subscription.id %}" class="btn">Retry charge</a>
            {% else %}
            Re-payment is not available
            {% endif %}
        </p>
        {% if current_or_pending_subscription %}
        <a href="{% url 'admin:billing_billingsubscription_change' current_or_pending_subscription.id %}">Current or pending subscription</a>
        {% endif %}
    </div>
</div>
<div class='details-search-row'>
    <div>
        <h3>Transactions list (latest 10)</h3>
        {% if transactions %}
        <table id="transactions">
            {% for transaction in transactions %}
            {% if forloop.first %}
            <thead>
            <tr>
                <th>ID</th>
                <th>Status</th>
                <th>Amount</th>
                <th>Currency</th>
                <th>Refunded amount</th>
                <th>External id</th>
                <th>Disputes</th>
                <th>Payment Processor</th>
                <th>Transaction Source</th>
                <th>Created</th>
                {% if can_refund %}
                <th>Link to refund</th>
                {% endif %}
            </tr>
            </thead>
            {% endif %}
            <tbody>
            <tr>
                <td><a href="{% url 'admin:billing_billingtransaction_change' transaction.id %}">{{ transaction.id }}</a></td>
                <td>{{ transaction.get_status_display }}</td>
                <td>{{ transaction.amount }}</td>
                <td>{{ transaction.currency }}</td>
            {% if transaction.amount_refunded %}
                <td><a href="{% url 'admin:billing_billingtransactionrefund_changelist'%}?&transaction_id={{ transaction.id }}">{{ transaction.amount_refunded }}</a></td>
            {% else %}
                <td>-</td>
            {% endif %}
                <td>{{ transaction.external_id }}</td>
            {% if transaction.has_dispute %}
                <td><a href="{% url 'admin:billing_billingtransactiondispute_changelist'%}?&transaction_id={{ transaction.id }}">link</a></td>
            {% else %}
                <td>-</td>
            {% endif %}
                <td>{{ transaction.get_payment_processor_display}}</td>
                <td>{{ transaction.get_transaction_source_display}}</td>
                <td>{{ transaction.created }}</td>
            {% if can_refund %}
                {% if transaction.can_be_refunded %}
                    <td><a href="{% url 'admin:billing_refund_transaction' transaction.id %}">Make refund</a></td>
                {% else %}
                    <td><a style="color: grey; cursor: help;" title="{{ transaction.get_additional_contraindication_reason }}">Refund Disabled</a></td>
                {% endif %}
            {% endif %}
            </tr>
            {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div>No transactions history...</div>
        {% endif %}
        <div id="braintree_transactions">
            <a style="margin-right:10px;" href="{% url 'admin:billing_billingtransaction_changelist' %}?business_id={{ object_id }}">All Transactions</a>
            <a style="margin-right:10px;" href="{% url 'admin:braintree_app_braintreetransaction_changelist' %}?business_id={{ object_id }}">All Braintree transactions</a>
            <a style="margin-right:10px;" href="{% url 'admin:stripe_app_paymentintent_changelist' %}?customer__booksy_id={{ stripe_booksy_id }}">All Stripe transactions</a>
            <a style="margin-right:10px;" href="{% url 'admin:migrate_stripe_transaction' object_id %}">Migrate Stripe Transaction</a>
            <a style="margin-right:10px;" href="{% url 'admin:stripe_app_setupintent_changelist'%}?customer__booksy_id={{ stripe_booksy_id }}">Stripe CrediCards Setup logs</a>
        </div>
    </div>
</div>
{% block sidebar %}
{% endblock sidebar %}
{% endblock content %}
