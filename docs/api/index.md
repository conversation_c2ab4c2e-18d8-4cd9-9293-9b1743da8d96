### How to write booksy-api docs

We use swagger notation.

Base specification is at [Swagger Spec version 1.2](https://github.com/wordnik/swagger-spec/blob/master/versions/1.2.md)

Swagger notation is obligatory but we are free to extend it.

Documentation is writen using YAML notation in Tornado request handlers docstrings.
Elements from class docstrings are inherited by methods docstrings.
We start swagger YAML using `swagger:` we can also end it and continue normal docstrings
using  `:swagger` tag.

We developed some special extensions to YAML swagger API:

 - `!import docs/api/models/business.yaml` - would load yaml file as this node content
 - `dontshow: smth`  - disable this method/class from api docs
 - methods wrapped with @session(api_key_required=True) require X-API-KEY header.
 - methods wrapped with @session(login_required=True) require X-ACCESS-TOKEN header.
 - methods wrapped with @json_request are tagged with "[JSON]" in summary
   and have "application/json" in their "consumes" and "produces" lists.
 - you don't have to write path, method and nickname for swagger operations, that are auto generated elements.
 - you can define models in docstrings (for models used only by docstring's owner)

Other useful information:

 - `docs/api/api.yaml` is a main served element of API
 - `docs/api/partners_api.yaml` is a main served element of Partners API
 - `docs/api/api_root.yaml` is a main API object with paths
 - `docs/api/partners_api_root.yaml` is a main Partners API object with paths
 - `docs/api/models.yaml` is a list of active models of API
 - `docs/api/partners_models.yaml` is a list of active models of Partners API
 - `docs/api/models/*.yaml` there are active models (they can be also not active)
 - if you see `X0`, `X1`, ..., `XN` in path params you need to declare parameter with `paramType: path`
