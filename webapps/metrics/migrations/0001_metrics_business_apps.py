# Generated by Django 2.0.13 on 2019-10-07 12:22

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('business', '0239_add_city_and_zipcode_fields_to_bci'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessApplication',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'applications',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.Char<PERSON><PERSON>(max_length=2), size=None
                    ),
                ),
                ('fingerprint', models.CharField(max_length=32)),
                (
                    'business',
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='business.Business',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
    ]
