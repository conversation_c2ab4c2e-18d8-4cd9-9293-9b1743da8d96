# Generated by Django 2.2.13 on 2020-07-08 08:41

from django.db import migrations, models
import django.db.models.deletion
import lib.fields.phone_number


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0042_auto_20200615_0947'),
        ('voucher', '0021_auto_20200513_1051'),
    ]

    operations = [
        migrations.AddField(
            model_name='voucher',
            name='waitting_for_recipient',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='voucherchangelog',
            name='waitting_for_recipient',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='voucherorder',
            name='friends_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='voucherorder',
            name='friends_phone',
            field=lib.fields.phone_number.BooksyPhoneNumberField(
                blank=True, max_length=50, null=True
            ),
        ),
        migrations.CreateModel(
            name='VoucherClaim',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('hash', models.CharField(db_index=True, max_length=100)),
                (
                    'consumed',
                    models.DateTimeField(blank=True, null=True, verbose_name='Used (UTC)'),
                ),
                (
                    'from_user',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='given_vouchers',
                        to='user.User',
                    ),
                ),
                (
                    'to_user',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='claimed_vouchers',
                        to='user.User',
                    ),
                ),
                (
                    'voucher',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='voucher_claims',
                        to='voucher.Voucher',
                    ),
                ),
                (
                    'voucher_order',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='order_claims',
                        to='voucher.VoucherOrder',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
    ]
