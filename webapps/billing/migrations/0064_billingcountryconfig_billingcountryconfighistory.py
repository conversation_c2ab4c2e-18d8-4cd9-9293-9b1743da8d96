# Generated by Django 4.1.7 on 2023-04-13 09:53

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import webapps.billing.models.config
import webapps.billing.models.managers


class Migration(migrations.Migration):
    dependencies = [
        ('user', '0062_userinternaldata_account_deletion_execution_method'),
        ('billing', '0063_billingoneoffcharge_billingoneoffchargeproduct_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillingCountryConfig',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'country',
                    models.CharField(
                        default=webapps.billing.models.config.get_default_api_country,
                        max_length=2,
                        unique=True,
                    ),
                ),
                (
                    'long_subscription_3m_precentage_discount',
                    models.SmallIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    'long_subscription_6m_precentage_discount',
                    models.SmallIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    'long_subscription_12m_precentage_discount',
                    models.SmallIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ('long_subscription_show_discount', models.BooleanField(blank=True, default=False)),
            ],
            options={
                'verbose_name': 'Billing Country Config',
                'verbose_name_plural': 'Billing Country Config',
            },
            managers=[
                ('objects', webapps.billing.models.managers.AutoUpdateAndAutoAddHistoryManager()),
            ],
        ),
        migrations.CreateModel(
            name='BillingCountryConfigHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingcountryconfig',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
    ]
