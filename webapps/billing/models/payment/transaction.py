import logging
from decimal import Decimal

from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>

from lib.models import ArchiveModel
from webapps import consts
from webapps.billing.entities.transaction import (
    BillingTransactionEntity,
    BusinessBillingEntity,
    BillingCreditCardEntity,
)
from webapps.billing.models import BillingCountryConfig
from webapps.billing.enums import (
    PaymentProcessorType,
    TransactionResponseType,
    TransactionSource,
    TransactionStatus,
)


__all__ = [
    'BillingTransaction',
]


_logger = logging.getLogger('booksy.billing')


class BillingTransaction(ArchiveModel):
    """Transaction model independent from payment processor."""

    class Meta:
        verbose_name = 'Subscription transaction'
        verbose_name_plural = 'Subscription transactions'

    payment_method = models.ForeignKey(
        'billing.BillingPaymentMethod',
        on_delete=models.PROTECT,
        related_name='transactions',
        null=True,
        blank=True,
    )
    subscription = models.ForeignKey(
        'billing.BillingSubscription',
        on_delete=models.PROTECT,
        related_name='transactions',
        null=True,
        blank=True,
    )
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.DO_NOTHING,
        related_name='billing_transactions',
    )
    billing_cycle = models.ForeignKey(
        'billing.BillingCycle',
        on_delete=models.PROTECT,
        related_name='transactions',
        null=True,
        blank=True,
    )
    status = models.CharField(max_length=1, choices=TransactionStatus.choices())
    amount = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    currency = models.CharField(max_length=3)
    response_code = models.CharField(max_length=4, null=True, editable=False)
    response_text = models.CharField(max_length=255, null=True, editable=False)
    response_type = models.CharField(
        max_length=1, choices=TransactionResponseType.choices(), null=True
    )
    response_errors = JSONField(null=True, blank=True, editable=False)
    external_id = models.CharField(
        max_length=255,
        db_index=True,
        null=True,
        blank=True,
    )
    extra_data = JSONField(default=dict)
    payment_processor = models.CharField(
        max_length=1,
        choices=PaymentProcessorType.choices(),
        default=PaymentProcessorType.BRAINTREE.value,
        editable=False,
        blank=True,
        null=True,
    )
    transaction_source = models.CharField(
        max_length=30,
        choices=TransactionSource.choices(),
        default=TransactionSource.BILLING_SUBSCRIPTION.value,
        editable=False,
        blank=True,
        null=True,
    )

    # pylint: disable=too-many-arguments
    # pylint: disable=too-many-positional-arguments
    @classmethod
    def create_skipped(
        cls,
        business_id: int,
        subscription_id: int,
        billing_cycle_id: int,
        amount: Decimal,
        currency: str,
    ):
        skipped = cls.objects.create(
            status=TransactionStatus.SKIPPED,
            amount=amount,
            currency=currency,
            business_id=business_id,
            subscription_id=subscription_id,
            billing_cycle_id=billing_cycle_id,
            payment_processor=None,
        )
        return skipped.id

    @property
    def billing_transaction_entity(self) -> BillingTransactionEntity:
        return BillingTransactionEntity(
            id=self.id,
            credit_card=BillingCreditCardEntity(
                last_4_digits=self.payment_method.credit_card.last_4_digits,
                expiration_month=self.payment_method.credit_card.expiration_date.month,
                expiration_year=self.payment_method.credit_card.expiration_date.year,
                cardholder_name=self.payment_method.credit_card.cardholder_name,
                card_type=self.payment_method.credit_card.card_type,
            ),
            amount=str(self.amount),
            currency=self.currency,
            business=BusinessBillingEntity(
                id=self.business.id,
                created_at=self.business.created,
                updated_at=self.business.updated,
                longitude=self.business.longitude,
                latitude=self.business.latitude,
                owner_first_name=self.business.owner.first_name,
                owner_last_name=self.business.owner.last_name,
                phone_number=self.business.phone,
                email=self.business.public_email,
                name=self.business.name,
                country=self.business.country_code,
                city=self.business.city,
                postal_code=self.business.zipcode,
                address=self.business.address,
                state=self.business.state,
            ),
        )

    def can_be_refunded(self):
        if self.is_created_before_stripe_migration():
            return False
        if (
            self.status == TransactionStatus.CHARGED
            and self.transaction_source == TransactionSource.BILLING_SUBSCRIPTION
        ):
            return True
        return False

    def is_created_before_stripe_migration(self) -> bool:
        billing_config = BillingCountryConfig.get_from_cache()
        if stripe_migration_date := billing_config.stripe_migration_date:
            return self.created < stripe_migration_date
        return False

    def get_additional_contraindication_reason(self) -> str:
        if self.is_created_before_stripe_migration():
            return (
                'The transaction creation date is before the Stripe account migration, '
                'therefore, the refund is only possible via the Stripe app.'
            )
        return '-'
