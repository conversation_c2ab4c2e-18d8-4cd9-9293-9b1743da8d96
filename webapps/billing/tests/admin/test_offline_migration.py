from datetime import datetime, timed<PERSON>ta
from unittest.mock import patch

import pytest
from django.conf import settings
from django.test import override_settings
from django.urls.base import reverse
from model_bakery import baker
from pytz import UTC
from rest_framework.status import HTTP_200_OK, HTTP_403_FORBIDDEN
from segment.analytics.client import Client

from lib.tools import tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.forms import BillingOfflineMigrationForm
from webapps.billing.models import (
    BillingBusinessOffer,
    BillingOfflineMigration,
    BillingOfflineMigrationRequest,
    BillingProductOffer,
)
from webapps.billing.tests.utils import billing_business
from webapps.business.models import Business
from webapps.purchase.models import Subscription
from webapps.segment.utils import get_track_common_params_for_tests


@pytest.mark.freeze_time(datetime(2022, 8, 8, tzinfo=UTC))
class TestOfflineMigrationAdmin(DjangoTestCase):
    @property
    def url(self):
        return reverse('admin:billing_billingofflinemigration_changelist')

    @property
    def url_add(self):
        return reverse('admin:billing_billingofflinemigration_add')

    @staticmethod
    def url_delete(_id):
        return reverse('admin:billing_billingofflinemigration_delete', args=(_id,))

    def setUp(self):
        super().setUp()
        self.login_admin()

        self.offline_business = baker.make(
            Business,
            has_new_billing=False,
            payment_source=Business.PaymentSource.OFFLINE,
            status=Business.Status.PAID,
        )
        self.offline_business.owner.cell_phone = '2137420'
        self.offline_business.owner.email = '<EMAIL>'
        self.offline_business.owner.save()
        self.business1 = billing_business()
        self.business2 = baker.make(
            Business,
            has_new_billing=False,
            payment_source=Business.PaymentSource.ITUNES,
            status=Business.Status.PAID,
        )
        self.offer1 = baker.make(
            BillingProductOffer,
            default=False,
            active=True,
            name='raz',
        )
        self.offer2 = baker.make(
            BillingProductOffer,
            default=False,
            active=True,
            name='dwa',
        )
        self.offer_default = baker.make(
            BillingProductOffer,
            default=True,
            active=True,
        )

    @patch.object(Client, 'track')
    def test_add_offline_migration_view(self, analytics_track_mock):
        data = dict(
            business=self.offline_business.id,
            offer=self.offer1.id,
        )
        resp = self.client.post(
            self.url_add,
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'added successfully')
        self.assertEqual(BillingOfflineMigration.objects.count(), 1)
        self.assertEqual(BillingOfflineMigrationRequest.objects.count(), 1)
        self.assertEqual(BillingBusinessOffer.objects.count(), 1)
        event_params = get_track_common_params_for_tests(self.offline_business)
        analytics_track_mock.assert_called_with(
            **{
                'user_id': event_params['user_id'],
                'anonymous_id': None,
                'event': 'Business_Offline_Migration_Started',
                'properties': {
                    'country': event_params['country'],
                    'user_role': event_params['user_role'].value,
                    'offer_type': event_params['offer_type'],
                    'business_id': event_params['business_id'],
                    'user_id': event_params['user_id'],
                    'email': event_params['email'],
                    'phone': event_params['phone'],
                },
            }
        )

    def test_add_offline_migration_with_billing_business(self):
        data = dict(
            business=self.business1.id,
            offer=self.offer1.id,
        )
        resp = self.client.post(
            self.url_add,
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'Business is already migrated to new billing')

    def test_add_offline_migration_with_no_offline_business(self):
        data = dict(
            business=self.business2.id,
            offer=self.offer1.id,
        )
        resp = self.client.post(
            self.url_add,
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'Business does not have offline subscription')

    def test_add_offline_migration_twice_for_same_business(self):
        BillingOfflineMigration(
            business=self.offline_business,
            deleted=None,
        ).save()
        data = dict(
            business=self.offline_business.id,
            offer=self.offer1.id,
        )
        resp = self.client.post(
            self.url_add,
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'Migration for this business already exists.')

    def test_add_offline_migration_after_deletion(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow(),
            business=self.offline_business,
            deleted=tznow(),
        )
        data = dict(
            business=self.offline_business.id,
            offer=self.offer1.id,
        )
        resp = self.client.post(
            self.url_add,
            data=data,
            follow=True,
        )
        self.assertEqual(resp.status_code, HTTP_200_OK)
        self.assertContains(
            resp,
            f'The Migration offline/online “BillingOfflineMigration'
            f' object ({migration.id})” was added successfully',
        )

    def test_available_offers_choice_field(self):
        resp = self.client.get(
            self.url_add,
        )
        self.assertContains(resp, self.offer1)
        self.assertContains(resp, self.offer2)
        self.assertNotContains(resp, self.offer_default)

    def test_add_offline_migration_invalid_offer(self):
        data = dict(
            business=self.offline_business.id,
            offer='whatever',
        )
        resp = self.client.post(
            self.url_add,
            data=data,
            follow=True,
        )
        self.assertContains(
            resp,
            'Select a valid choice. That choice is not one of the available choices',
        )

    @patch(
        'webapps.billing.permissions.BillingUserPermission.is_billing_user',
        False,
    )
    def test_permissions_to_add_offline_migration(self):
        resp = self.client.post(
            self.url_add,
        )
        self.assertEqual(resp.status_code, HTTP_403_FORBIDDEN)

    @override_settings(SAVE_HISTORY=True)
    @patch.object(Client, 'track')
    def test_add_new_migration_campaign(self, analytics_track_mock):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow() - timedelta(hours=18),
            business=self.offline_business,
        )
        baker.make(
            BillingOfflineMigrationRequest,
            offline_migration=migration,
            operator=self.logged_user,
        )
        data = dict(
            action='add_new_migration_request',
            _selected_action=[migration.id],
        )
        resp = self.client.post(
            self.url,
            data=data,
            follow=True,
        )
        migration.refresh_from_db()
        self.assertContains(resp, 'Migration campaigns successfully extended')
        self.assertEqual(BillingOfflineMigrationRequest.objects.count(), 2)
        self.assertEqual(
            migration.migration_campaign_valid_to,
            tznow()
            + timedelta(
                days=settings.OFFLINE_TO_ONLINE_MIGRATION_CAMPAIGN_VALID_DAYS,
            ),
        )
        event_params = get_track_common_params_for_tests(self.offline_business)
        analytics_track_mock.assert_called_once()
        analytics_track_mock.assert_called_with(
            **{
                'user_id': event_params['user_id'],
                'anonymous_id': None,
                'event': 'Business_Offline_Migration_Started',
                'properties': {
                    'country': event_params['country'],
                    'user_role': event_params['user_role'].value,
                    'offer_type': event_params['offer_type'],
                    'business_id': event_params['business_id'],
                    'user_id': event_params['user_id'],
                    'email': event_params['email'],
                    'phone': event_params['phone'],
                },
            }
        )

    def test_add_new_migration_campaign_fail_cause_still_valid(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow() + timedelta(days=2),
            business=self.offline_business,
        )
        baker.make(
            BillingOfflineMigrationRequest,
            offline_migration=migration,
            operator=self.logged_user,
        )
        data = dict(
            action='add_new_migration_request',
            _selected_action=[migration.id],
        )
        resp = self.client.post(
            self.url,
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'Migration campaign is still active')
        self.assertEqual(BillingOfflineMigrationRequest.objects.count(), 1)

    def test_add_new_migration_campaign_fail_cause_business_agreed(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow() - timedelta(hours=18),
            business=self.offline_business,
            agreed_at=tznow(),
        )
        baker.make(
            BillingOfflineMigrationRequest,
            offline_migration=migration,
            operator=self.logged_user,
        )
        data = dict(
            action='add_new_migration_request',
            _selected_action=[migration.id],
        )
        resp = self.client.post(
            self.url,
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'Add new migration campaign')
        self.assertContains(resp, 'Business already agreed to migrate to billing')
        self.assertEqual(BillingOfflineMigrationRequest.objects.count(), 1)

    def test_add_new_migration_campaign_fail_cause_deleted(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow(),
            business=self.offline_business,
            deleted=tznow(),
        )
        baker.make(
            BillingOfflineMigrationRequest,
            offline_migration=migration,
            operator=self.logged_user,
        )
        data = dict(
            action='add_new_migration_request',
            _selected_action=[migration.id],
        )
        resp = self.client.post(
            self.url + '?deleted=any',
            data=data,
            follow=True,
        )
        self.assertContains(resp, 'Migration was already deleted ')
        self.assertEqual(BillingOfflineMigrationRequest.objects.count(), 1)

    def test_add_new_migration_campaign_no_permission(self):
        self.logged_user.groups.set([])
        self.logged_user.is_superuser = False
        self.logged_user.save()

        data = dict(
            action='add_new_migration_request',
            _selected_action=[],
        )
        resp = self.client.post(
            self.url,
            data=data,
        )
        self.assertEqual(resp.status_code, HTTP_403_FORBIDDEN)

    @override_settings(SAVE_HISTORY=True)
    def test_delete_selected_migrations(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow() + timedelta(days=21),
            business=self.offline_business,
            reminder=True,
        )
        request = baker.make(
            BillingOfflineMigrationRequest,
            offline_migration=migration,
            operator=self.logged_user,
        )
        data = dict(action='delete_selected', _selected_action=[migration.id], post='yes')
        resp = self.client.post(
            self.url,
            data=data,
            follow=True,
        )
        migration.refresh_from_db()
        request.refresh_from_db()
        self.assertContains(resp, 'Successfully deleted 1 Migration offline/online.')
        self.assertEqual(migration.migration_campaign_valid_to, tznow())
        self.assertFalse(migration.reminder)
        self.assertEqual(migration.deleted, tznow())
        self.assertEqual(request.deleted, tznow())

    def test_delete_selected_migrations_fail_already_migrated(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow() + timedelta(days=21),
            business=self.offline_business,
            agreed_at=tznow(),
        )
        data = dict(action='delete_selected', _selected_action=[migration.id], post='yes')
        resp = self.client.post(
            self.url,
            data=data,
            follow=True,
        )
        migration.refresh_from_db()
        self.assertContains(
            resp,
            'Some of selected migrations concern businesses '
            'that already have migrated to billing.',
        )
        self.assertEqual(migration.deleted, None)

    def test_delete_selected_migrations_fail_already_deleted(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow() + timedelta(days=21),
            business=self.offline_business,
            agreed_at=None,
            deleted=tznow(),
        )
        data = dict(action='delete_selected', _selected_action=[migration.id], post='yes')
        resp = self.client.post(
            self.url + '?deleted=any',
            data=data,
            follow=True,
        )
        self.assertContains(
            resp,
            'Some of selected migrations are already deleted.',
        )

    @override_settings(SAVE_HISTORY=True)
    def test_delete_migration(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow() + timedelta(days=21),
            business=self.offline_business,
            agreed_at=None,
            reminder=True,
        )
        request = baker.make(
            BillingOfflineMigrationRequest,
            offline_migration=migration,
            operator=self.logged_user,
        )

        data = dict(offline_migration=migration.id)
        resp = self.client.post(
            self.url_delete(migration.id),
            data=data,
            follow=True,
        )
        self.assertContains(
            resp,
            f'The Migration offline/online “BillingOfflineMigration'
            f' object ({migration.id})” was deleted successfully',
        )
        migration.refresh_from_db()
        request.refresh_from_db()
        self.assertEqual(migration.migration_campaign_valid_to, tznow())
        self.assertEqual(migration.deleted, tznow())
        self.assertFalse(migration.reminder)
        self.assertEqual(request.deleted, tznow())

    def test_delete_migration_fail_already_migrated(self):
        migration = baker.make(
            BillingOfflineMigration,
            migration_campaign_valid_to=tznow(),
            business=self.offline_business,
            agreed_at=tznow(),
        )
        data = dict(offline_migration=migration.id)
        resp = self.client.post(
            self.url_delete(migration.id),
            data=data,
            follow=True,
        )
        self.assertEqual(resp.status_code, HTTP_403_FORBIDDEN)

    def test_new_migration_set_expiry_on_active_subscription(self):
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            subscription = baker.make(
                Subscription,
                business=self.offline_business,
                source=Business.PaymentSource.OFFLINE,
                expiry=None,
            )
        form = BillingOfflineMigrationForm(
            data=dict(
                business=self.offline_business.id,
                offer=self.offer1.id,
                offline_subscription_end_date="2030-01-01",
            )
        )
        self.assertTrue(form.is_valid())
        form.save()
        subscription.refresh_from_db()
        self.assertEqual(subscription.expiry, datetime(2030, 1, 1, tzinfo=UTC))

    def test_new_migration_set_expiry_on_pending_subscription(self):
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            subscription = baker.make(
                Subscription,
                business=self.offline_business,
                source=Business.PaymentSource.OFFLINE,
                expiry=None,
                start=datetime(2022, 9, 9, tzinfo=UTC),
            )
        form = BillingOfflineMigrationForm(
            data=dict(
                business=self.offline_business.id,
                offer=self.offer1.id,
                offline_subscription_end_date="2030-01-01",
            )
        )
        self.assertTrue(form.is_valid())
        form.save()
        subscription.refresh_from_db()
        self.assertEqual(subscription.expiry, datetime(2022, 9, 9, tzinfo=UTC))

    def test_new_migration_do_not_override_expiry_if_end_date_empty(self):
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            subscription = baker.make(
                Subscription,
                business=self.offline_business,
                source=Business.PaymentSource.OFFLINE,
                expiry=datetime(2030, 1, 1, tzinfo=UTC),
            )
        form = BillingOfflineMigrationForm(
            data=dict(
                business=self.offline_business.id,
                offer=self.offer1.id,
                offline_subscription_end_date=None,
            )
        )
        self.assertTrue(form.is_valid())
        form.save()
        subscription.refresh_from_db()
        self.assertEqual(subscription.expiry, datetime(2030, 1, 1, tzinfo=UTC))
