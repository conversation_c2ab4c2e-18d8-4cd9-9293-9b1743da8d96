# pylint: disable=no-name-in-module
from django_socio_grpc.proto_serializers import ProtoSerializer
from rest_framework import serializers

from booksy_proto_ecommerce.pubsub.v1.copy_to_inventory_pb2 import (
    CopyToInventory,
    CatalogProduct,
    Supplier,
    Category,
)


class SupplierMessageSerializer(ProtoSerializer):
    class Meta:
        proto_class = Supplier

    id = serializers.UUIDField()
    name = serializers.CharField()
    tax_id = serializers.CharField(required=False, allow_blank=True)
    email = serializers.CharField(required=False, allow_blank=True)
    phone = serializers.CharField(required=False, allow_blank=True)
    country = serializers.CharField(required=False, allow_blank=True)
    zip_code = serializers.CharField(required=False, allow_blank=True)
    city = serializers.CharField(required=False, allow_blank=True)
    address = serializers.Char<PERSON>ield(required=False, allow_blank=True)


class CategoryMessageSerializer(ProtoSerializer):
    class Meta:
        proto_class = Category

    id = serializers.UUIDField()
    name = serializers.CharField()
    parent_id = serializers.UUIDField(required=False, allow_null=True)


class CatalogProductMessageSerializer(ProtoSerializer):
    class Meta:
        proto_class = CatalogProduct

    name = serializers.CharField()
    leaf_categories_ids = serializers.ListField(child=serializers.UUIDField())
    all_categories = CategoryMessageSerializer(many=True)
    sku = serializers.CharField()
    tax_rate = serializers.DecimalField(max_digits=5, decimal_places=2, required=False)
    gross_price = serializers.DecimalField(max_digits=5, decimal_places=2, required=False)
    net_price = serializers.DecimalField(max_digits=5, decimal_places=2, required=False)
    supplier = SupplierMessageSerializer(required=False, allow_null=True)
    ean = serializers.CharField()
    volume_unit = serializers.CharField()
    description = serializers.CharField()
    main_photo_url = serializers.URLField(required=False, allow_null=True)
    photo_urls = serializers.ListField(child=serializers.URLField())
    brand = serializers.CharField()
    supplier_product_id = serializers.UUIDField(required=False, allow_null=True)
    catalog_product_id = serializers.UUIDField()
    catalog_product_variant_id = serializers.UUIDField(required=False, allow_null=True)


class CopyToInventoryMessageSerializer(ProtoSerializer):
    class Meta:
        proto_class = CopyToInventory

    business_id = serializers.IntegerField()
    catalog_products = CatalogProductMessageSerializer(many=True)
