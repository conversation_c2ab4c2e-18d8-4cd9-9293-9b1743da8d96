from model_bakery import baker

from django.test import TestCase, override_settings
from lib.test_utils import get_in_memory_img

from country_config import Country

from webapps.business.baker_recipes import business_recipe, category_recipe, basic_staffer_recipe
from webapps.ecommerce.enums import EcommercePermissionsEnum
from webapps.ecommerce.models import NotificationCampaign
from webapps.ecommerce.tasks import send_notification_campaign
from webapps.notification.elasticsearch import NotificationDocument
from webapps.notification.enums import NotificationTarget
from webapps.notification.models import NotificationSchedule


class EcommerceNotificationCampaignTestCase(TestCase):
    def test_send_notification_to_business_ids(self):
        business = business_recipe.make()
        business.categories.add(business.primary_category)
        notification = NotificationCampaign.objects.create(
            business_ids=[business.id],
            message_line_1='line1',
            message_line_2='line2',
            message_line_3='line3',
            relevance=2,
            deeplink='deeplink_value',
            image=get_in_memory_img(),
        )
        notification_document_search = (
            NotificationDocument.search()
            .query('term', business_id=business.id)
            .query('term', user_id=business.owner.id)
            .query('term', notif_type='EcommerceNotification')
        )
        self.assertEqual(len(notification_document_search.execute(ignore_cache=True)), 0)
        send_notification_campaign(notification.id)
        send_notification_campaign(notification.id)
        NotificationDocument.refresh_index()
        self.assertEqual(len(notification_document_search.execute(ignore_cache=True)), 1)
        self.assertTrue(
            NotificationSchedule.objects.filter(
                task_id=f'EcommerceNotification,{notification.id}',
            ).exists()
        )
        notification.refresh_from_db()
        search_result = notification_document_search.execute(ignore_cache=True)[0]
        self.assertDictEqual(
            search_result.content.to_dict(),
            {
                'messages': [
                    notification.message_line_1,
                    notification.message_line_2,
                    notification.message_line_3,
                ],
                'photo_url': '',
                'size': 1,
                'target_id': notification.deeplink,
                'target_type': NotificationTarget.DEEPLINK.value,
            },
        )
        self.assertEqual(
            search_result.image,
            'https://img.booksy.pm/test-bucket/ecommerce_notification_campaign/mocked-image.png',
        )

    @override_settings(API_COUNTRY=Country.PL)
    def test_send_notification_based_on_permission(self):
        business = business_recipe.make()
        business.categories.add(business.primary_category)
        resource_with_perm = basic_staffer_recipe.make(business=business)
        baker.make(
            'EcommercePermission',
            resource=resource_with_perm,
            permission=EcommercePermissionsEnum.STORE_AVAILABLE.value,
        )
        resource_without_perm = basic_staffer_recipe.make(business=business)
        notification = NotificationCampaign.objects.create(
            business_ids=[business.id],
            message_line_1='line1',
            message_line_2='line2',
            message_line_3='line3',
            relevance=2,
            deeplink='deeplink_value',
        )
        send_notification_campaign(notification.id)
        NotificationDocument.refresh_index()
        notification_document_search = (
            NotificationDocument.search()
            .query(
                'term',
                business_id=business.id,
            )
            .query(
                'term',
                user_id=business.owner.id,
            )
            .query(
                'term',
                notif_type='EcommerceNotification',
            )
            .execute(ignore_cache=True)
        )
        self.assertEqual(len(notification_document_search), 0)

        notification_document_search = (
            NotificationDocument.search()
            .query(
                'term',
                business_id=business.id,
            )
            .query(
                'term',
                user_id=resource_with_perm.staff_user.id,
            )
            .query(
                'term',
                notif_type='EcommerceNotification',
            )
            .execute(ignore_cache=True)
        )
        self.assertEqual(len(notification_document_search), 1)

        notification_document_search = (
            NotificationDocument.search()
            .query(
                'term',
                business_id=business.id,
            )
            .query(
                'term',
                user_id=resource_without_perm.staff_user.id,
            )
            .query(
                'term',
                notif_type='EcommerceNotification',
            )
            .execute(ignore_cache=True)
        )
        self.assertEqual(len(notification_document_search), 0)

    def test_send_notification_to_categories(self):
        business = business_recipe.make()
        business.categories.add(business.primary_category)
        notification = NotificationCampaign.objects.create(
            message_line_1='line1',
            message_line_2='line2',
            message_line_3='line3',
            relevance=2,
            deeplink='deeplink_value',
        )
        notification.business_categories.add(business.primary_category)
        notification_document_search = (
            NotificationDocument.search()
            .query('term', business_id=business.id)
            .query('term', user_id=business.owner.id)
            .query('term', notif_type='EcommerceNotification')
        )
        self.assertEqual(len(notification_document_search.execute(ignore_cache=True)), 0)
        send_notification_campaign(notification.id)
        NotificationDocument.refresh_index()
        self.assertEqual(len(notification_document_search.execute(ignore_cache=True)), 1)
        self.assertTrue(
            NotificationSchedule.objects.filter(
                task_id=f'EcommerceNotification,{notification.id}',
            ).exists()
        )
        notification.refresh_from_db()

    def test_send_notification_to_business_and_not_having_category(self):
        business = business_recipe.make()
        business.categories.add(business.primary_category)
        notification = NotificationCampaign.objects.create(
            business_ids=[business.id],
            message_line_1='line1',
            message_line_2='line2',
            message_line_3='line3',
            relevance=2,
            deeplink='deeplink_value',
        )
        notification.business_categories.add(category_recipe.make())
        notification_document_search = (
            NotificationDocument.search()
            .query('term', business_id=business.id)
            .query('term', user_id=business.owner.id)
            .query('term', notif_type='EcommerceNotification')
        )
        self.assertEqual(len(notification_document_search.execute(ignore_cache=True)), 0)
        send_notification_campaign(notification.id)
        NotificationDocument.refresh_index()
        self.assertEqual(len(notification_document_search.execute(ignore_cache=True)), 0)
        self.assertFalse(
            NotificationSchedule.objects.filter(
                task_id=f'EcommerceNotification,{notification.id}',
            ).exists()
        )
        notification.refresh_from_db()
