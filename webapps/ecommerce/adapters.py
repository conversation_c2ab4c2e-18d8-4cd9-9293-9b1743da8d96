from django.db.models import Exists, OuterRef
from webapps.ecommerce import EcommerceConfig
from webapps.ecommerce.models import EcommercePermission
from webapps.ecommerce.enums import EcommercePermissionsEnum
from webapps.ecommerce.consts import (
    ECOMMERCE_ALWAYS_ALLOWED_ROLES,
    ECOMMERCE_PERMISSION_MANAGERS,
    PERMISSION_ANNOTATION_FIELD_NAME,
)
from webapps.ecommerce.messages.sync_business_data import EcommerceBusinessDataSyncMessage


class EcommercePermissionAdapter:

    @staticmethod
    def is_valid_store_access_level(staff_access_level):
        return staff_access_level in ECOMMERCE_ALWAYS_ALLOWED_ROLES

    @staticmethod
    def can_modify_store_access(staff_access_level):
        return staff_access_level in ECOMMERCE_PERMISSION_MANAGERS

    @staticmethod
    def has_store_access(resource):
        if EcommerceConfig.is_allowed_in_api_country():
            if hasattr(resource, PERMISSION_ANNOTATION_FIELD_NAME):  # annotated value
                return getattr(resource, PERMISSION_ANNOTATION_FIELD_NAME)
            return resource.ecommerce_permissions.filter(
                permission=EcommercePermissionsEnum.STORE_AVAILABLE.value
            ).exists()
        return False

    @staticmethod
    def has_store_access_annotation():
        if EcommerceConfig.is_allowed_in_api_country():
            return {
                PERMISSION_ANNOTATION_FIELD_NAME: Exists(
                    EcommercePermission.objects.filter(
                        resource_id=OuterRef('pk'),
                        permission=EcommercePermissionsEnum.STORE_AVAILABLE.value,
                    )
                )
            }
        return {}

    @staticmethod
    def update_store_permission(resource, has_store_available=None):
        if EcommerceConfig.is_allowed_in_api_country():
            if has_store_available is True:
                EcommercePermission.objects.get_or_create(
                    resource=resource,
                    permission=EcommercePermissionsEnum.STORE_AVAILABLE.value,
                )
            elif has_store_available is False:
                if not EcommercePermissionAdapter.is_valid_store_access_level(
                    resource.staff_access_level
                ):  # don't allow to revoke access to roles like owner
                    try:
                        permission = EcommercePermission.objects.get(
                            resource=resource,
                            permission=EcommercePermissionsEnum.STORE_AVAILABLE.value,
                        )
                    except EcommercePermission.DoesNotExist:
                        pass
                    else:
                        permission.delete()


class EcommerceBusinessAdapter:
    @staticmethod
    def update_business_data(business, dirty_fields):
        if EcommerceConfig.is_allowed_in_api_country() and business.active:
            if dirty_fields.keys() & set(["name", "owner", "primary_category"]):
                EcommerceBusinessDataSyncMessage(business).publish()
