import logging

from google.cloud.pubsub_v1.subscriber.message import Message as SubscriberMessage

# pylint: disable=no-name-in-module
from booksy_proto_ecommerce.pubsub.v1.ecommerce_email_pb2 import EcommerceEmail

# pylint: enable=no-name-in-module
from webapps.ecommerce.serializers.ecommerce_email import EcommerceEmailMessageSerializer
from webapps.ecommerce.subscribers.base import BaseEcommerceSubscriber
from lib.email import send_email
from lib.pdf_render import PDFRenderer


logger = logging.getLogger('booksy.pubsub')


class EcommerceEmailSubscriber(BaseEcommerceSubscriber):
    topic_proto_class = EcommerceEmail
    _abstract = False

    def render_pdf_file(self, pdf_data):
        return PDFRenderer.render_pdf(
            template_name='ecommerce_order_summary',
            pdf_body_content=pdf_data['body_content'],
            pdf_style_content=pdf_data['body_style'],
        )

    def handle(self, data, message: SubscriberMessage | None = None):
        logger.info('Processing ecommerce email: %s', data)

        serializer = EcommerceEmailMessageSerializer(message=data)
        if not serializer.is_valid(raise_exception=False):
            logger.error("Invalid ecommerce email data: %s", serializer.errors)
            return
        attachments = []
        try:
            if pdf_data := serializer.validated_data.get('pdf_data'):
                attachments.append(
                    (pdf_data['name'], self.render_pdf_file(pdf_data), 'application/pdf')
                )
            send_email(
                serializer.validated_data['to_addresses'],
                serializer.validated_data['body'],
                subject=serializer.validated_data['subject'],
                attachments=attachments,
            )
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Error while processing ecommerce email: %s", e)
