import inspect
import os

import typing as t
from abc import ABC

from ddtrace import tracer

from django.db import transaction
from django.db.models import QuerySet
from django.conf import settings

from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models.category import BusinessCategory
from webapps.df_creator.enums import DFGroupCodename
from webapps.df_creator.models import DigitalFlyerCategory, DigitalFlyerGroup
from webapps.df_creator.utils import create_backgrounds


COUNTRY_CODE = settings.API_COUNTRY


class AbstractScriptRunner(ABC):
    def run(self):
        """Should be overridden with script body"""
        raise NotImplementedError()

    def execute_script(self):
        script_file = inspect.getfile(self.__class__)
        script_filename = os.path.basename(script_file)

        if settings.DD_AGENT_ENABLED:
            with tracer.trace(
                name='script.run', service=f'script-runner-{COUNTRY_CODE}', resource=script_filename
            ):
                self.run()
        else:
            self.run()


class DBScriptRunner(AbstractScriptRunner):
    """
    For running database scripts. Note the `atomic` flag,
    the script will be executed in database transaction if
    it will be True.
    """

    atomic = True

    def run(self):
        """Should be overridden with script body"""
        raise NotImplementedError()

    def execute_script(self):
        if self.atomic:
            with transaction.atomic():
                super().execute_script()
        else:
            super().execute_script()


class CustomScriptRunner(AbstractScriptRunner):
    """
    For running non-database scripts
    """

    def run(self):
        """Should be overridden with script body"""
        raise NotImplementedError()


class DFCreatorScriptRunner(DBScriptRunner):
    """
    For robust digital flyer importing
    """

    GroupsType = t.Dict[DFGroupCodename, DigitalFlyerGroup]
    CreatorReturnType = t.Tuple[DigitalFlyerCategory, GroupsType]
    CreatorType = t.Callable[[t.Any], CreatorReturnType]

    creators: t.List[CreatorType] = NotImplemented
    args: t.List = None
    kwargs: t.Dict = None

    extra_backgrounds: t.Dict[BusinessCategoryEnum, t.Tuple[int, str]] = {}
    business_categories: QuerySet(BusinessCategory) = NotImplemented

    def run(self):
        if not self.args:
            self.args = []
        if not self.kwargs:
            self.kwargs = {}

        for index, create_fnc in enumerate(self.creators, 1):
            print(f'Creating DF category {index}/{len(self.creators)}')
            df_category, df_groups = create_fnc(*self.args, **self.kwargs)

            print(f'Created category {df_category.name}')
            print(f'Created {len(df_groups)} subcategories:', end='\n\t')
            print(*df_groups, sep='\n\t')
        print('Successfully created DF categories\n')

        for internal_name, (n_images, suffix) in self.extra_backgrounds.items():

            print(
                f'Creating additional {n_images} backgrounds '
                f'for specific business category {internal_name}'
            )
            category = self.business_categories.filter(
                internal_name=internal_name,
            ).first()
            if not category:
                print(
                    f'Missing category {internal_name}.'
                    f'{n_images} will not be created. Suffix {suffix}'
                )
                continue
            create_backgrounds(
                codename=internal_name + suffix,
                n_images=n_images,
                business_categories=[category],
            )
        print('Successfully created extra DF backgrounds\n')
