from django.conf import settings

from lib.elasticsearch.default_connection import connection
from webapps.script_runner.mixins import ReusableScript
from webapps.script_runner.runners import CustomScriptRunner


class Script(ReusableScript, CustomScriptRunner):
    version = 1

    def run(self):
        if settings.LIVE_DEPLOYMENT:
            return
        connection.indices.delete(index="*appointment_history_index*", request_timeout=3600, ignore=404)
        connection.indices.delete(index="*resource_history_index*", request_timeout=3600, ignore=404)
