from unittest.mock import patch

from datetime import datetime
from django.utils import timezone

from model_bakery import baker
import pytest

from webapps.billing.models import BillingTransaction
from webapps.billing.models.subscription import BillingCycle
from webapps.billing.enums import PaymentProcessorType, TransactionSource, TransactionStatus

from webapps.business.baker_recipes import business_recipe

from webapps.navision.enums import InvoiceService, InvoicePaymentSource
from webapps.navision.models import Invoice, InvoiceItem

from webapps.script_runner.scripts.script__update_missing_hof_invoice_charge__es_gb import (
    Script,
)


# pylint: disable=redefined-outer-name


@pytest.fixture
def business(db):
    return business_recipe.make()


@pytest.fixture
def billing_cycle(db, business):
    return baker.make(
        BillingCycle,
        business=business,
        date_start=timezone.make_aware(datetime(2025, 3, 1)),
        date_end=timezone.make_aware(datetime(2025, 3, 31)),
    )


@pytest.fixture
def invoice(db, business):
    return baker.make(Invoice, business_id=business.id, source=InvoicePaymentSource.HOF)


@pytest.fixture
def unpaid_saas_invoice_item(db, invoice, billing_cycle):
    return baker.make(
        InvoiceItem,
        invoice=invoice,
        charge_completed=False,
        service=InvoiceService.SAAS,
        billing_cycle_start=billing_cycle.date_start,
    )


@pytest.mark.django_db
@patch(
    "webapps.script_runner.scripts.script__update_missing_hof_invoice_charge__es_gb."
    "update_invoice_item_as_charged_task"
)
def test_updates_item_when_hof_saas_invoice_got_paid(
    mocked_update_invoice_item_as_charged_task,
    business,
    billing_cycle,
    unpaid_saas_invoice_item,
):
    charged_transaction = baker.make(
        BillingTransaction,
        business=business,
        billing_cycle=billing_cycle,
        status=TransactionStatus.CHARGED,
        transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        external_id='pi_123456789',
        payment_processor=PaymentProcessorType.STRIPE,
    )

    Script().run()

    mocked_update_invoice_item_as_charged_task.delay.assert_called_once_with(
        charged_transaction.external_id,
        charged_transaction.created,
        charged_transaction.billing_cycle_id,
        charged_transaction.payment_processor,
    )


@pytest.mark.django_db
@patch(
    "webapps.script_runner.scripts.script__update_missing_hof_invoice_charge__es_gb."
    "update_invoice_item_as_charged_task"
)
def test_does_not_update_when_transaction_failed(
    mocked_update_invoice_item_as_charged_task,
    business,
    billing_cycle,
    unpaid_saas_invoice_item,
):
    baker.make(
        BillingTransaction,
        business=business,
        billing_cycle=billing_cycle,
        status=TransactionStatus.FAILED,
        transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        external_id='pi_123456789',
        payment_processor=PaymentProcessorType.STRIPE,
    )

    Script().run()

    mocked_update_invoice_item_as_charged_task.delay.assert_not_called()
