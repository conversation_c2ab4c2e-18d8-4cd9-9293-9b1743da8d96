from typing import Optional

from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import serializers

from lib.serializers import DynamicFieldsSerializerMixin, IdAndNameSerializer
from lib.tools import tznow
from webapps.booking.serializers.fields import BookingDate<PERSON>ime<PERSON>ield
from webapps.business.enums import CustomData
from webapps.business.serializers import (
    BusinessInReviewSerializer,
)
from webapps.reviews.models import Review, ReviewFeedback, ReviewPhoto
from webapps.user.serializers import ReviewResponseCustomerSerializer
from webapps.user.tools import truncate_last_name


# <editor-fold desc="Helper function that should be part of serializer">
def fetch_feedbacks(
    review_ids: list[int], user: 'User' = None, fingerprint: str = None
) -> dict[int, dict]:
    """Moved from CustomerReviewMixin. fetch_feedbacks
    Returns dict like this:
    {
        review_id: {
            'feedback': current_user_feedback,
            'feedback_status': {
                'Y': count_of_yes_votes,
                'N': count_of_yes_votes,
                'I': count_of_invalid_votes,
            },
        },
        review_id_next: ...
    }
    """
    result = dict(
        (
            review_id,
            {
                'feedback_status': {
                    ReviewFeedback.Feedback.YES: 0,
                    ReviewFeedback.Feedback.NO: 0,
                    ReviewFeedback.Feedback.INAPPROPRIATE: 0,
                }
            },
        )
        for review_id in review_ids
    )
    review_feedbacks = list(
        ReviewFeedback.objects.filter(review_id__in=review_ids).values(
            'review_id',
            'feedback',
            'user_id',
            'fingerprint',
        )
    )

    for review_feedback in review_feedbacks:
        entry = result[review_feedback['review_id']]
        entry['feedback_status'][review_feedback['feedback']] += 1
        if (
            user
            and review_feedback['user_id'] == user.id
            or (
                review_feedback['fingerprint'] == fingerprint and review_feedback['user_id'] is None
            )
        ):
            entry['feedback'] = review_feedback['feedback']
    return result


def format_review_with_feedback(
    formatted_reviews: list[dict],
    feedbacks: dict[int, dict],
    blacklisted_user_ids: Optional[set[int]] = None,
) -> None:
    """Modify formatted_reviews"""
    if blacklisted_user_ids is None:
        blacklisted_user_ids = set()
    for review in formatted_reviews:
        if review.get('user', {}).get('id') is None:
            review['user'].pop('id', None)
            review['feedback_status'] = {
                ReviewFeedback.Feedback.YES: 0,
                ReviewFeedback.Feedback.NO: 0,
                ReviewFeedback.Feedback.INAPPROPRIATE: 0,
            }
            continue
        review.update(feedbacks[review['id']])

        if review.get('user', {}).get('id') in blacklisted_user_ids:
            review['user']['blacklisted'] = True


# </editor-fold>


class CustomerReviewServiceSerializer(IdAndNameSerializer):
    treatment_id = serializers.IntegerField(required=False)


class SimpleCustomerReviewSerializer(
    DynamicFieldsSerializerMixin,
    serializers.ModelSerializer,
):
    class Meta:
        model = Review
        fields = [
            'business',
            'rank',
            'review',
            'services',
            'staff',
            'subbooking',
            'title',
            'user',
            'verified',
        ]
        extra_kwargs = {'subbooking': {'write_only': True}}

    def validate(self, attrs):
        if self.instance:
            if self.instance.created <= (tznow() - settings.CAN_EDIT_REVIEW_LIMIT_PERIOD):
                raise serializers.ValidationError(
                    _('Review cannot be edited after 30 days from creation'),
                )
        return super().validate(attrs)


class CustomerReviewSerializer(SimpleCustomerReviewSerializer):
    class Meta(SimpleCustomerReviewSerializer.Meta):
        fields = SimpleCustomerReviewSerializer.Meta.fields + [
            'appointment_date',
            'created',
            'id',
            'reply_content',
            'reply_updated',
            'source',
            'updated',
            'anonymized',
        ]

    appointment_date = BookingDateTimeField(read_only=True, source='subbooking.booked_from')
    business = serializers.SerializerMethodField()
    created = BookingDateTimeField()
    reply_updated = BookingDateTimeField()
    services = CustomerReviewServiceSerializer(many=True)
    staff = IdAndNameSerializer(many=True)
    updated = BookingDateTimeField()
    user = serializers.SerializerMethodField()
    anonymized = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._serialized_business_cache = {}

    def get_business(self, instance):
        return self._serialized_business(instance.business)

    @staticmethod
    def get_anonymized(instance):
        return instance.business.custom_data.get(
            CustomData.ANONYMIZE_CUSTOMER_DATA_IN_REVIEWS, False
        )

    def _serialized_business(self, business):
        if business.id in self._serialized_business_cache:
            return self._serialized_business_cache[business.id]
        data = BusinessInReviewSerializer(instance=business).data
        self._serialized_business_cache[business.id] = data
        return data

    def get_user(self, instance):
        if instance.user is not None:
            return ReviewResponseCustomerSerializer(instance.user).data

        return {
            'id': None,
            'first_name': instance.first_name,
            'last_name': truncate_last_name(instance.last_name),
        }

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        ret['staff'] = list({v['id']: v for v in ret['staff']}.values())
        prefetched_objects = getattr(instance, '_prefetched_objects_cache', {})
        if 'review_photos' in prefetched_objects:
            review_photos = instance.review_photos.all()
        else:
            review_photos = instance.review_photos.all().select_related('photo').order_by('id')

        ret['photos'] = CustomerReviewPhotoSerializer(
            instance=review_photos,
            many=True,
        ).data

        return ret


class CustomerReviewPhotoSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReviewPhoto
        fields = ('id', 'url', 'published')
        read_only_fields = fields

    url = serializers.URLField(source='photo.full_url')
    published = serializers.BooleanField(source='photo.published_to')


class CustomerReviewSerializerForBusiness(CustomerReviewSerializer):
    def get_user(self, instance):
        user_data = super().get_user(instance)
        business = self.context.get('business')
        if business and instance.user:
            bci = instance.user.business_customer_infos.filter(business=business).first()
            if bci and bci.photo:
                user_data['avatar'] = bci.photo.full_url

        return user_data


class ModerateCustomerReviewSerializerForBusiness(CustomerReviewSerializerForBusiness):
    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if ret['anonymized']:
            ret['user']['first_name'] = _('Anonymous')
            ret['user']['last_name'] = ''
            ret['user']['avatar'] = ''

        return ret


ERROR_MESSAGES = {'blank': _('This field is required.')}


class ReviewReplySerializer(serializers.ModelSerializer):
    class Meta:
        model = Review
        fields = ('reply_content', 'reply_updated')
        extra_kwargs = {
            'reply_content': {
                'allow_blank': False,
                'required': True,
                'error_messages': ERROR_MESSAGES,
            },
        }
