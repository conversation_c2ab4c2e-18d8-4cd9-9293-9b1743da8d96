from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from unittest.mock import Mock, patch
from typing import Iterable

import pytest
from django.conf import settings
from django.test import TestCase
from model_bakery import baker
from pytz import UTC
from segment.analytics import Client

from lib.deeplink.branchio.client import Branch<PERSON><PERSON>lient
from lib.facebook.service import FacebookEventService
from lib.segment_analytics.api import SegmentAnalyticsWrapper
from lib.segment_analytics.enums import EventType
from lib.tools import id_to_external_api, tznow
from service.tests import dict_assert
from webapps.billing.enums import PaymentProcessorType, TransactionStatus
from webapps.billing.models import BillingSubscription, BillingTransaction
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.business.models.category import BusinessCategory
from webapps.invoicing.baker_recipes import customer_recipe
from webapps.purchase.models import Subscription, SubscriptionListing
from webapps.segment.enums import AnalyticEventEnums
from webapps.segment.tasks import (
    analytics_1st_paid_status_achieved_branchio_task,
    analytics_paid_status_achieved_branchio_task,
    analytics_subscription_payment_succeeded_task,
    analytics_cb_created_count_in_days_for_business_segment_task,
    analytics_churn_reason_updated_task,
    send_analytics_1st_cb_created_for_business_to_facebook,
)
from webapps.segment.utils import get_user_role_by_id
from webapps.user.baker_recipes import user_recipe
from webapps.user.models import User

from webapps.segment import tasks


class TestAnalyticsSubscriptionPaymentSucceeded(TestCase):
    def setUp(self) -> None:
        self.business = baker.make(
            Business,
            has_new_billing=True,
            owner__email="<EMAIL>",
            status=Business.Status.PAID,
        )
        self.subscription = baker.make(
            BillingSubscription, business=self.business, date_start=datetime(2021, 3, 3, tzinfo=UTC)
        )
        self.transaction = baker.make(
            BillingTransaction,
            business=self.business,
            subscription=self.subscription,
            payment_processor=PaymentProcessorType.BRAINTREE,
            status=TransactionStatus.CHARGED,
            amount=Decimal("1.3"),
            currency="PLN",
        )

    @patch.object(Client, "identify")
    @patch.object(Client, "track")
    def test_event_send_ok(self, mocked_track, mocked_identify):
        baker.make(
            BillingTransaction,
            business=self.business,
            subscription=self.subscription,
            status=TransactionStatus.CHARGED,
        )
        analytics_subscription_payment_succeeded_task.run(
            transaction_id=self.transaction.id,
            context={
                "event_type": EventType.BUSINESS,
                "business_id": self.business.id,
            },
        )
        dict_assert(
            mocked_track.call_args_list[0][1],
            {
                "event": "Subscription_Payment_Succeed",
                "properties": {
                    "country": settings.API_COUNTRY,
                    "business_id": id_to_external_api(self.business.id),
                    "business_name": self.business.name,
                    "business_admin_status": Business.Status.PAID.name,
                    "paid_from": "03.03.2021",
                    "email": "<EMAIL>",
                    "user_id": id_to_external_api(self.business.owner.id),
                    "subscription_payment_method": Business.PaymentSource.BRAINTREE.label,
                    "is_first_payment": False,
                },
            },
        )
        dict_assert(
            mocked_identify.call_args[1]["traits"],
            {
                "business_id": id_to_external_api(self.business.id),
                "country": settings.API_COUNTRY,
                "event_name": "python.analytics_subscription_payment_succeeded_task",
                "email": "<EMAIL>",
                "subscription_billing_amount": "1.30",
                "subscription_billing_currency": "PLN",
                "user_id": id_to_external_api(self.business.owner.id),
                "user_role": get_user_role_by_id(self.business.owner.id),
            },
        )

    @patch.object(Client, "identify")
    @patch.object(Client, "track")
    def test_event_send_first_payment(self, mocked_track, mocked_identify):
        analytics_subscription_payment_succeeded_task.run(
            transaction_id=self.transaction.id,
            context={
                "event_type": EventType.BUSINESS,
                "business_id": self.business.id,
            },
        )
        dict_assert(
            mocked_track.call_args_list[0][1],
            {
                "event": "Subscription_Payment_Succeed",
                "properties": {
                    "country": settings.API_COUNTRY,
                    "business_id": id_to_external_api(self.business.id),
                    "business_name": self.business.name,
                    "business_admin_status": Business.Status.PAID.name,
                    "paid_from": "03.03.2021",
                    "email": "<EMAIL>",
                    "user_id": id_to_external_api(self.business.owner.id),
                    "subscription_payment_method": Business.PaymentSource.BRAINTREE.label,
                    "is_first_payment": True,
                },
            },
        )
        dict_assert(
            mocked_identify.call_args[1]["traits"],
            {
                "business_id": id_to_external_api(self.business.id),
                "country": settings.API_COUNTRY,
                "event_name": "python.analytics_subscription_payment_succeeded_task",
                "email": "<EMAIL>",
                "subscription_billing_amount": "1.30",
                "subscription_billing_currency": "PLN",
                "user_id": id_to_external_api(self.business.owner.id),
                "user_role": get_user_role_by_id(self.business.owner.id),
            },
        )


class TestAnalyticsPaidStatusAchieved(TestCase):
    def setUp(self) -> None:
        self.business = baker.make(
            Business,
            has_new_billing=True,
            owner__email="<EMAIL>",
            status=Business.Status.TRIAL,
            primary_category=baker.make(BusinessCategory, internal_name="Hair Salon"),
        )
        old_data = BusinessChange.extract_vars(self.business)
        self.change = baker.make(BusinessChange)
        self.business.status = Business.Status.PAID
        self.business.save()
        BusinessChange.add(
            business=self.business,
            obj=self.business,
            old_obj_vars=old_data,
            metadata={},
        )

    @patch.object(BranchIOClient, "track_event")
    def test_event_send_ok(self, mocked_track_event):
        analytics_paid_status_achieved_branchio_task.run(
            business_id=self.business.id,
            context={
                "event_type": EventType.BUSINESS,
                "business_id": self.business.id,
            },
        )
        self.assertEqual(mocked_track_event.call_args[1]["event_name"], "Paid_Status_Achieved")
        dict_assert(
            mocked_track_event.call_args[1]["event_data"],
            {
                "country": settings.API_COUNTRY,
                "business_phone": "",
                "user_role": get_user_role_by_id(self.business.owner.id),
                "business_id": id_to_external_api(self.business.id),
                "user_id": id_to_external_api(self.business.owner.id),
                "email": "<EMAIL>",
                "primary_category": "Hair Salon",
                "business_admin_status": Business.Status.PAID.name,
                "fingerprint": "UNKNOWN",
                "paid_counter": 1,
            },
        )


class TestAnalytics1PaidStatusAchievedBranchio(TestCase):
    def setUp(self) -> None:
        self.user = baker.make(User, email="<EMAIL>")
        self.business = baker.make(
            Business,
            has_new_billing=True,
            owner=self.user,
            status=Business.Status.PAID,
            primary_category=baker.make(BusinessCategory, internal_name="Hair Salon"),
        )

    @patch.object(BranchIOClient, "track_event")
    def test_event_send_new_billing_ok(self, mocked_track_event):
        analytics_1st_paid_status_achieved_branchio_task.run(
            business_id=self.business.id,
            context={
                "business_id": self.business.id,
            },
        )
        self.assertEqual(mocked_track_event.call_args[1]["event_name"], "1st_Paid_Status_Achieved")
        dict_assert(
            mocked_track_event.call_args[1]["event_data"],
            {
                "country": "us",
                "user_role": "Owner",
                "business_id": id_to_external_api(self.business.id),
                "user_id": id_to_external_api(self.user.id),
                "email": "<EMAIL>",
                "primary_category": "Hair Salon",
                "business_admin_status": "PAID",
                "subscription_payment_method": "Booksy Billing",
            },
        )

    @patch.object(BranchIOClient, "track_event")
    def test_event_send_old_billing_ok(self, mocked_track_event):
        self.business.has_new_billing = False
        self.business.save()
        baker.make(
            Subscription,
            business=self.business,
            source=Business.PaymentSource.OFFLINE,
            product=baker.make(SubscriptionListing),
        )
        analytics_1st_paid_status_achieved_branchio_task.run(
            business_id=self.business.id,
            context={
                "business_id": self.business.id,
            },
        )
        self.assertEqual(mocked_track_event.call_args[1]["event_name"], "1st_Paid_Status_Achieved")
        self.assertEqual(
            "Offline", mocked_track_event.call_args[1]["event_data"]["subscription_payment_method"]
        )

    @patch.object(BranchIOClient, "track_event")
    def test_event_send_with_wrong_status(self, mocked_track_event):
        self.business.status = Business.Status.TRIAL
        self.business.save()

        analytics_1st_paid_status_achieved_branchio_task.run(
            business_id=self.business.id,
            context={
                "business_id": self.business.id,
            },
        )

        self.assertFalse(mocked_track_event.called)


@pytest.mark.django_db
@patch.object(FacebookEventService, "send_event_with_custom_data")
@patch.object(
    tasks,
    "_get_cb_common_properties_optimized",
    return_value={
        "service_price": [Decimal("35.0"), Decimal("24.1")],
        "booking_score": Decimal("137.00"),
    },
)
def test_decimal_conversion_for_facebook(_mock_get_cb_properties, mock_send_event_with_custom_data):
    business = business_recipe.make(owner=user_recipe.make())
    bci = customer_recipe.make(business=business)
    appointment = create_appointment(
        business=business,
        booked_for=bci,
        type=Appointment.TYPE.CUSTOMER,
        status=Appointment.STATUS.FINISHED,
        customer_email=bci.email,
    )

    send_analytics_1st_cb_created_for_business_to_facebook(appointment_id=appointment.id)
    assert mock_send_event_with_custom_data.call_args.kwargs["data"]["service_price"] == [
        35.0,
        24.1,
    ]
    assert mock_send_event_with_custom_data.call_args.kwargs["data"]["booking_score"] == 137.0


class TestAnalyticsCBCreatedCountInDaysForBusinessTask(TestCase):

    def setUp(self) -> None:
        self.business = business_recipe.make(owner=user_recipe.make())
        self.business.active_from = tznow()
        self.business.save()

    @patch.object(SegmentAnalyticsWrapper, "track")
    def test_basic_event(self, track_segment_mock):
        for _ in range(4):
            create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        appointment = create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        analytics_cb_created_count_in_days_for_business_segment_task.run(
            appointment_id=appointment.id, context={'business_id': self.business.id}
        )
        self.assertEqual(
            track_segment_mock.call_args_list[0][0][0], AnalyticEventEnums.FIFTH_CB_IN_FOURTEEN_DAYS
        )
        self.assertDictEqual(
            track_segment_mock.call_args_list[0][0][1],
            {
                'country': settings.API_COUNTRY,
                'email': self.business.owner.email,
                'business_id': id_to_external_api(self.business.id),
                'user_id': id_to_external_api(self.business.owner.id),
            },
        )

    @patch.object(SegmentAnalyticsWrapper, "track")
    def test_not_enough_appointments(self, track_segment_mock):
        for _ in range(3):
            create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        appointment = create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        analytics_cb_created_count_in_days_for_business_segment_task.run(
            appointment_id=appointment.id, context={'business_id': self.business.id}
        )
        self.assertFalse(track_segment_mock.called)

    @patch.object(SegmentAnalyticsWrapper, "track")
    def test_too_many_appointments(self, track_segment_mock):
        for _ in range(5):
            create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        appointment = create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        analytics_cb_created_count_in_days_for_business_segment_task.run(
            appointment_id=appointment.id, context={'business_id': self.business.id}
        )
        self.assertFalse(track_segment_mock.called)

    @patch.object(SegmentAnalyticsWrapper, "track")
    def test_appointment_after_14_days_appointments(self, track_segment_mock):
        self.business.active_from = tznow() - timedelta(days=14)
        self.business.save()
        for _ in range(4):
            create_appointment(
                business=self.business,
                type=Appointment.TYPE.CUSTOMER,
                created=tznow() - timedelta(days=8),
            )
        appointment = create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        analytics_cb_created_count_in_days_for_business_segment_task.run(
            appointment_id=appointment.id, context={'business_id': self.business.id}
        )
        self.assertFalse(track_segment_mock.called)

    @patch.object(SegmentAnalyticsWrapper, "track")
    def test_modify_params(self, track_segment_mock):
        for _ in range(2):
            create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        appointment = create_appointment(business=self.business, type=Appointment.TYPE.CUSTOMER)
        analytics_cb_created_count_in_days_for_business_segment_task.run(
            appointment_id=appointment.id,
            context={'business_id': self.business.id},
            event_name='Foo',
            appointment_cnt=3,
            days_from_business_active=4,
        )
        self.assertEqual(track_segment_mock.call_args_list[0][0][0], 'Foo')
        self.assertDictEqual(
            track_segment_mock.call_args_list[0][0][1],
            {
                'country': settings.API_COUNTRY,
                'email': self.business.owner.email,
                'business_id': id_to_external_api(self.business.id),
                'user_id': id_to_external_api(self.business.owner.id),
            },
        )


# pylint: disable=redefined-outer-name


@pytest.fixture
def mock_segment_facade() -> Iterable[Mock]:
    with patch("webapps.segment.tasks.SegmentFacade") as mock:
        yield mock


@pytest.mark.django_db
def test_analytics_churn_reason_updated_task_calls_component_service(
    mock_segment_facade: Mock,
    any_reason_id: int = 123,
):
    # pylint: disable=no-value-for-parameter
    analytics_churn_reason_updated_task(reason_id=any_reason_id)
    # pylint: enable=no-value-for-parameter
    mock_segment_facade().update_churn_reason.assert_called_once_with(reason_id=any_reason_id)
