from datetime import datetime, UTC

from django.core.management import BaseCommand
from django.db.models import Q
from tqdm import tqdm

from lib.payment_gateway.enums import BalanceTransactionType
from lib.tools import chunker
from webapps.payment_gateway.models import (
    BalanceTransaction,
    Wallet,
)
from webapps.payment_gateway.tasks import synchronize_booksy_wallet_balance_transactions_task


class Command(BaseCommand):
    help = 'Sync previous balance transactions with Booksy Wallet'
    chunk_size = 1000
    date_format = '%Y-%m-%d'

    def add_arguments(self, parser):
        parser.add_argument('--async', action='store_true')
        parser.add_argument('--business_id', dest='business_id')
        parser.add_argument(
            '--date_from', dest='date_from', help='Format: YYYY-MM-DD', required=True
        )
        parser.add_argument('--date_to', dest='date_to', help='Format: YYYY-MM-DD', required=True)
        parser.add_argument(
            '--transaction_type',
            dest='transaction_type',
            nargs='+',
            help=(
                'Filter by transaction types. Valid options: payment, refund, '
                'dispute, payout, fee, transfer_fund'
            ),
        )

    def handle(self, *args, **options):
        filter_args = []
        filter_kwargs = {}

        business_id = options.get('business_id')
        transaction_types = options.get('transaction_type')

        if not (options.get('date_from') and options.get('date_to')):
            self.stdout.write(
                self.style.ERROR(
                    'You have to specify time range: date_from or date_to can not be blank'
                )
            )
            return

        # Validate transaction types if provided
        if transaction_types:
            valid_types = [choice.value for choice in BalanceTransactionType]
            invalid_types = [t for t in transaction_types if t not in valid_types]
            if invalid_types:
                self.stdout.write(
                    self.style.ERROR(
                        f'Invalid transaction types: {", ".join(invalid_types)}. '
                        f'Valid options are: {", ".join(valid_types)}'
                    )
                )
                return

        if business_id:
            try:
                wallet = Wallet.objects.get(business_id=business_id)
            except Wallet.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(
                        f'Wallet associated with this business_id={business_id} does not exist.'
                    )
                )
                return

            filter_args = [
                Q(Q(receiver=wallet) | Q(sender=wallet)),
            ]
        if options.get('date_to'):
            filter_kwargs['created__lte'] = datetime.strptime(
                options['date_to'], self.date_format
            ).astimezone(UTC)

        if options.get('date_from'):
            filter_kwargs['created__gte'] = datetime.strptime(
                options['date_from'], self.date_format
            ).astimezone(UTC)

        # Filter by transaction types if provided
        if transaction_types:
            filter_kwargs['transaction_type__in'] = transaction_types

        bt_ids = (
            BalanceTransaction.objects.filter(
                *filter_args,
                **filter_kwargs,
            )
            .order_by('updated')
            .values_list('id', flat=True)
        )

        for ids in tqdm(
            chunker(bt_ids.iterator(), self.chunk_size),
            total=bt_ids.count() // self.chunk_size,
        ):
            if options.get('async'):
                synchronize_booksy_wallet_balance_transactions_task.delay(ids)
            else:
                synchronize_booksy_wallet_balance_transactions_task.run(ids)
