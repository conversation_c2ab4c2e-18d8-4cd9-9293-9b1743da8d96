import datetime
import uuid
from dataclasses import asdict
from decimal import Decimal

import pytest
from dateutil import tz
from django.test import TestCase
from mock import patch, MagicMock
from model_bakery import baker

from lib.payment_gateway.entities import PaymentSplitsEntity
from lib.payment_gateway.enums import BalanceTransactionStatus, BalanceTransactionType, DisputeType
from lib.payment_gateway.enums import PaymentStatus as WalletPaymentStatus
from lib.payment_gateway.enums import WalletOwnerType
from lib.payment_providers.entities import (
    PaymentEntity,
    PaymentOperationEntity,
    PayoutEntity,
    PayoutDetailsEntity,
    TransferFundEntity,
)
from lib.payment_providers.enums import (
    PaymentOperationStatus,
    PaymentOperationType,
    TransferFundStatus,
)
from lib.payment_providers.enums import PaymentStatus as PaymentProvidersPaymentStatus
from lib.payment_providers.events import (
    payment_providers_payment_operation_created_event,
    payment_providers_payment_operation_updated_event,
    payment_providers_payment_updated_event,
    payment_providers_payout_created_event,
    payment_providers_payout_updated_event,
)
from lib.payments.enums import PaymentProviderCode, PayoutStatus, PayoutType
from webapps.payment_gateway.models import (
    BalanceTransaction,
    Payment,
    Payout,
    Wallet,
    WalletFeeSettings,
)


# pylint: disable=invalid-name, line-too-long


@pytest.mark.django_db
class PaymentProvidersActionsTests(TestCase):
    @patch('webapps.payment_gateway.actions.send_balance_transaction_message', MagicMock())
    def setUp(self) -> None:
        super().setUp()
        self.cus_wallet = baker.make(
            Wallet,
            owner_type=WalletOwnerType.CUSTOMER,
            user_id=123,
            customer_id=uuid.uuid4(),
        )
        self.biz_wallet = baker.make(
            Wallet,
            owner_type=WalletOwnerType.BUSINESS,
            business_id=123,
            account_holder_id=uuid.uuid4(),
        )
        self.booksy_wallet = baker.make(Wallet, owner_type=WalletOwnerType.BOOKSY)
        self.wallet_fee_settings_stripe = baker.make(
            WalletFeeSettings,
            default=True,
            payment_provider_code=PaymentProviderCode.STRIPE,
            fast_payout_provision_percentage=Decimal(10.00),
            fast_payout_provision_fee=100,
            regular_payout_provision_percentage=Decimal(0),
            regular_payout_provision_fee=100,
        )
        self.wallet_fee_settings_adyen = baker.make(
            WalletFeeSettings,
            default=True,
            payment_provider_code=PaymentProviderCode.ADYEN,
            fast_payout_provision_percentage=Decimal(10.00),
            fast_payout_provision_fee=100,
            regular_payout_provision_percentage=Decimal(0),
            regular_payout_provision_fee=100,
        )

    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_updated_event.send'
    )
    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_created_event.send'
    )
    def test_payment_providers_payment_updated_handler(self, *_):
        bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.PAYMENT,
            status=BalanceTransactionStatus.PROCESSING,
            sender=self.cus_wallet,
            receiver=self.biz_wallet,
            external_id=uuid.uuid4(),
        )
        payment = baker.make(
            Payment,
            balance_transaction=bt,
            status=WalletPaymentStatus.NEW,
            payment_splits=PaymentSplitsEntity(percentage_fee=Decimal(0), fixed_fee=0).as_dict(),
            refund_splits={},
            dispute_splits={},
        )

        for payment_providers_payment_status, wallet_payment_status, bt_status in [
            (
                PaymentProvidersPaymentStatus.SENT_FOR_AUTHORIZATION,
                WalletPaymentStatus.SENT_FOR_AUTHORIZATION,
                BalanceTransactionStatus.PROCESSING,
            ),
            (
                PaymentProvidersPaymentStatus.ACTION_REQUIRED,
                WalletPaymentStatus.ACTION_REQUIRED,
                BalanceTransactionStatus.PROCESSING,
            ),
            (
                PaymentProvidersPaymentStatus.AUTHORIZED,
                WalletPaymentStatus.AUTHORIZED,
                BalanceTransactionStatus.PROCESSING,
            ),
            (
                PaymentProvidersPaymentStatus.SENT_FOR_CAPTURE,
                WalletPaymentStatus.SENT_FOR_CAPTURE,
                BalanceTransactionStatus.PROCESSING,
            ),
            (
                PaymentProvidersPaymentStatus.CAPTURED,
                WalletPaymentStatus.CAPTURED,
                BalanceTransactionStatus.SUCCESS,
            ),
        ]:
            payment_entity = PaymentEntity(
                id=bt.external_id,
                status=payment_providers_payment_status,
                amount=123,
                fee_amount=11,
                auto_capture=True,
            )
            payment_providers_payment_updated_event.send(asdict(payment_entity))
            bt.refresh_from_db()
            payment.refresh_from_db()

            self.assertEqual(payment.status, wallet_payment_status)
            self.assertEqual(bt.status, bt_status)

    @patch(
        'webapps.payment_gateway.services.balance_transaction.payment_gateway_balance_transaction_updated_event.send'
    )
    @patch(
        'webapps.payment_gateway.services.dispute.payment_gateway_balance_transaction_created_event.send'
    )
    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.process_transfer_fund')
    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.initialize_transfer_fund')
    def test_payment_providers_payment_operation_created_handler(
        self, initialize_transfer_fund_mock, *_
    ):
        initialize_transfer_fund_mock.side_effect = lambda **_: TransferFundEntity(
            id=uuid.uuid4(),
            amount=10,
            status=TransferFundStatus.PROCESSING,
        )
        bt = baker.make(
            BalanceTransaction,
            status=BalanceTransactionStatus.SUCCESS,
            transaction_type=BalanceTransactionType.PAYMENT,
            payment_provider_code=PaymentProviderCode.STRIPE,
            sender=self.cus_wallet,
            receiver=self.biz_wallet,
            external_id=uuid.uuid4(),
            amount=155,
        )
        _payment = baker.make(
            Payment,
            balance_transaction=bt,
            status=WalletPaymentStatus.CAPTURED,
            payment_splits={},
            refund_splits={},
            dispute_splits={"percentage_fee": 0.20, "fixed_fee": 51},
        )

        payment_operation_entity = PaymentOperationEntity(
            id=uuid.uuid4(),
            payment_id=bt.external_id,
            type=PaymentOperationType.CHARGEBACK,
            amount=17,
            status=PaymentOperationStatus.PROCESSING,
        )
        payment_providers_payment_operation_created_event.send(asdict(payment_operation_entity))
        self.assertEqual(bt.child_balance_transactions.count(), 1)
        dispute_bt: BalanceTransaction = bt.child_balance_transactions.last()
        self.assertTrue(dispute_bt.pk)
        self.assertEqual(dispute_bt.transaction_type, BalanceTransactionType.DISPUTE)
        self.assertEqual(dispute_bt.status, BalanceTransactionStatus.SUCCESS)
        self.assertEqual(dispute_bt.amount, 17)
        self.assertTrue(dispute_bt.dispute.pk)
        self.assertEqual(dispute_bt.dispute.type, DisputeType.CHARGEBACK)
        self.assertEqual(initialize_transfer_fund_mock.call_count, 2)

    @patch(
        'webapps.payment_gateway.services.balance_transaction.payment_gateway_balance_transaction_updated_event.send'
    )
    @patch(
        'webapps.payment_gateway.services.dispute.payment_gateway_balance_transaction_created_event.send'
    )
    def test_payment_providers_payment_operation_updated_handler(self, *_):
        bt = baker.make(
            BalanceTransaction,
            status=BalanceTransactionStatus.PROCESSING,
            transaction_type=BalanceTransactionType.DISPUTE,
            sender=self.cus_wallet,
            receiver=self.biz_wallet,
            external_id=uuid.uuid4(),
            amount=155,
        )

        payment_operation_entity = PaymentOperationEntity(
            id=bt.external_id,
            payment_id=uuid.uuid4(),
            type=PaymentOperationType.CHARGEBACK,
            amount=155,
            status=PaymentOperationStatus.SUCCESS,
        )
        payment_providers_payment_operation_updated_event.send(asdict(payment_operation_entity))

        bt.refresh_from_db()
        self.assertEqual(bt.status, BalanceTransactionStatus.SUCCESS)

    @patch(
        'webapps.payment_gateway.services.payout.payment_gateway_balance_transaction_updated_event.send'
    )
    @patch(
        'webapps.payment_gateway.services.payout.payment_gateway_balance_transaction_created_event.send'
    )
    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.process_transfer_fund')
    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.initialize_transfer_fund')
    def test_payment_providers_payout_updated_event_handler(self, initialize_transfer_mock, *_):
        initialize_transfer_mock.return_value = TransferFundEntity(
            id=uuid.uuid4(),
            amount=500,
            status=TransferFundStatus.PROCESSING,
        )
        bt = baker.make(
            BalanceTransaction,
            status=BalanceTransactionStatus.PROCESSING,
            transaction_type=BalanceTransactionType.PAYOUT,
            payment_provider_code=PaymentProviderCode.STRIPE,
            sender=self.biz_wallet,
            external_id=uuid.uuid4(),
            amount=155,
        )
        _payout = baker.make(
            Payout,
            status=PayoutStatus.IN_PAYMENT_PROCESSOR,
            splits={},
            balance_transaction=bt,
        )

        arrival = datetime.datetime(2022, 7, 1, tzinfo=tz.gettz('UTC'))
        payout_entity = PayoutEntity(
            id=bt.external_id,
            payment_provider_code=PaymentProviderCode.STRIPE,
            account_holder_id=self.biz_wallet.account_holder_id,
            status=PayoutStatus.IN_TRANSIT,
            amount=155,
            expected_arrival_date=arrival,
            payout_type=PayoutType.REGULAR,
        )
        payment_providers_payout_updated_event.send(asdict(payout_entity))

        bt.refresh_from_db()
        self.assertEqual(bt.status, BalanceTransactionStatus.PROCESSING)
        self.assertEqual(bt.payout.status, PayoutStatus.IN_TRANSIT)
        self.assertEqual(bt.payout.expected_arrival_date, arrival)

        arrival = datetime.datetime(2022, 7, 5, tzinfo=tz.gettz('UTC'))
        payout_entity = PayoutEntity(
            id=bt.external_id,
            payment_provider_code=PaymentProviderCode.STRIPE,
            account_holder_id=self.biz_wallet.account_holder_id,
            status=PayoutStatus.ARRIVED,
            amount=160,
            expected_arrival_date=arrival,
            payout_type=PayoutType.REGULAR,
        )
        payment_providers_payout_updated_event.send(asdict(payout_entity))

        bt.refresh_from_db()
        self.assertEqual(bt.status, BalanceTransactionStatus.SUCCESS)
        self.assertEqual(bt.amount, 160)
        self.assertEqual(bt.payout.status, PayoutStatus.ARRIVED)
        self.assertEqual(bt.payout.expected_arrival_date, arrival)
        self.assertEqual(BalanceTransaction.objects.count(), 2)
        self.assertEqual(
            set(BalanceTransaction.objects.values_list("transaction_type", flat=True)),
            {BalanceTransactionType.PAYOUT, BalanceTransactionType.FEE},
        )

    @patch(
        'webapps.payment_gateway.services.payout.payment_gateway_balance_transaction_updated_event.send'
    )
    @patch(
        'webapps.payment_gateway.services.payout.payment_gateway_balance_transaction_created_event.send'
    )
    @patch('webapps.payment_gateway.services.payout.PaymentProvidersAdapter.get_payout_details')
    def test_payment_providers_payout_created_event_handler(self, get_payout_details_mock, *_):
        bt_external_ids = [uuid.uuid4() for _ in range(12)]
        get_payout_details_mock.return_value = PayoutDetailsEntity(
            payment_operations=[
                PaymentOperationEntity(id=bt_id, amount=5, payment_id="", status="", type="")
                for bt_id in bt_external_ids[:4]
            ],
            payments=[
                PaymentEntity(id=bt_id, amount=5, fee_amount=1, status="", auto_capture=True)
                for bt_id in bt_external_ids[4:8]
            ],
            transfer_funds=[
                TransferFundEntity(
                    id=bt_id,
                    amount=5,
                    status=TransferFundStatus.PROCESSING,
                )
                for bt_id in bt_external_ids[8:]
            ],
        )
        bt_ids = []
        for bt_external_id in bt_external_ids:
            bt = baker.make(BalanceTransaction, external_id=bt_external_id)
            bt_ids.append(bt.id)

        payout_entity = PayoutEntity(
            id=uuid.uuid4(),
            payment_provider_code=PaymentProviderCode.STRIPE,
            account_holder_id=self.biz_wallet.account_holder_id,
            status=PayoutStatus.IN_TRANSIT,
            amount=111,
            payout_type=PayoutType.REGULAR,
        )
        payment_providers_payout_created_event.send(asdict(payout_entity))

        self.assertEqual(
            BalanceTransaction.objects.count(),
            12 + 1,  # previous baker bts and a payout
        )
        payout_bt = BalanceTransaction.objects.get(external_id=payout_entity.id)
        self.assertEqual(payout_bt.transaction_type, BalanceTransactionType.PAYOUT)
        self.assertEqual(payout_bt.amount, 111)
        self.assertEqual(payout_bt.sender, self.biz_wallet)
        self.assertEqual(payout_bt.payment_provider_code, PaymentProviderCode.STRIPE)
        self.assertEqual(payout_bt.payout.status, PayoutStatus.IN_TRANSIT)
        for bt in BalanceTransaction.objects.filter(id__in=bt_ids):
            self.assertEqual(bt.paid_out_in_payout, payout_bt.payout)
