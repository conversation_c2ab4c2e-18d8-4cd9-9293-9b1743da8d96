import uuid

import pytest
from django.test import TestCase
from mock import patch
from mock.mock import <PERSON>Mock
from model_bakery import baker

from lib.payment_gateway.enums import BalanceTransactionType, PaymentMethodType, WalletOwnerType
from webapps.payment_gateway.exceptions import InvalidOperation
from webapps.payment_gateway.models import BalanceTransaction, Wallet
from webapps.payment_gateway.ports import PaymentGatewayPort


@pytest.mark.django_db
class PaymentGatewayPortTests(TestCase):
    @patch('webapps.payment_gateway.actions.send_balance_transaction_message', MagicMock())
    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.capture_payment')
    def test_capture_payment(self, capture_payment_mock):
        random_wallet = baker.make(Wallet, owner_type=WalletOwnerType.BUSINESS)
        receiver_wallet = baker.make(Wallet, owner_type=WalletOwnerType.BUSINESS)
        bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.PAYMENT,
            receiver=receiver_wallet,
            external_id=uuid.uuid4(),
            payment_method=PaymentMethodType.CARD,
        )
        with self.assertRaises(InvalidOperation):
            PaymentGatewayPort.capture_payment(
                balance_transaction_id=bt.id,
                wallet_id=random_wallet.id,
            )
        self.assertEqual(capture_payment_mock.call_count, 0)

        PaymentGatewayPort.capture_payment(
            balance_transaction_id=bt.id,
            wallet_id=receiver_wallet.id,
        )
        self.assertEqual(capture_payment_mock.call_count, 1)
        capture_payment_mock.assert_called_with(
            payment_id=bt.external_id,
        )
