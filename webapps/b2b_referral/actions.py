from django.conf import settings

from lib.events import lazy_event_receiver, async_event_receiver
from lib.feature_flag.feature import MarketPayB2BReferralSettingEnabledFlag

from webapps.b2b_referral.events import (
    invited_business_paid_for_subscription_event,
    invited_business_signed_up_event,
)
from webapps.b2b_referral.models import B2BReferral
from webapps.b2b_referral.notifications import (
    EncourageToReferralsIfActiveSubscriberNotification,
    InvitedBusinessPaidNotificationToInvited,
    InvitedBusinessPaidNotificationToReferrer,
    InvitedBusinessSignedUpNotificationToInvited,
    InvitedBusinessSignedUpNotificationToReferrer,
    PendingReferralRewardWaitingForKYCVerification,
)
from webapps.b2b_referral.utils import mark_referral_deal_as_pending_reward
from webapps.business.events import business_status_changed_event
from webapps.business.models import Business


__all__ = ['mark_referral_deal_as_pending_reward_handler']


@async_event_receiver(business_status_changed_event)
def mark_referral_deal_as_pending_reward_handler(
    business_id: int,
    new_status: Business.Status,
    old_status: Business.Status | None = None,
    **kwargs,
):
    if (
        old_status in [Business.Status.TRIAL, Business.Status.TRIAL_BLOCKED]
        and new_status == Business.Status.PAID
    ):
        mark_referral_deal_as_pending_reward(business_id)


if settings.POPUP_NOTIFICATIONS_ENABLED and settings.POPUP_PHASE4:
    __all__ += [
        'invited_business_signed_up_receiver',
        'invited_business_paid_for_subscription_receiver',
    ]

    @lazy_event_receiver(invited_business_signed_up_event)
    def invited_business_signed_up_receiver(referral: B2BReferral, **_kwargs) -> None:
        InvitedBusinessSignedUpNotificationToReferrer(referral).send()
        InvitedBusinessSignedUpNotificationToInvited(referral).send()

    @lazy_event_receiver(invited_business_paid_for_subscription_event)
    def invited_business_paid_for_subscription_receiver(referral: B2BReferral, **_kwargs) -> None:
        if MarketPayB2BReferralSettingEnabledFlag():
            market_pay_global_setting = settings.MARKET_PAY_B2B_REFERRAL_ENABLED
        else:
            market_pay_global_setting = settings.MARKET_PAY_ENABLED

        if market_pay_global_setting:
            InvitedBusinessPaidNotificationToReferrer(referral).send()
        InvitedBusinessPaidNotificationToInvited(referral).send()

        EncourageToReferralsIfActiveSubscriberNotification(
            referral.invited,
        ).schedule(days_ahead=10)

        if referral.base_referral_setting.both_award_flow and not referral.invited.completed_kyc:
            PendingReferralRewardWaitingForKYCVerification(
                referral.invited,
            ).schedule(days_ahead=3)

        if not referral.referrer.completed_kyc:
            PendingReferralRewardWaitingForKYCVerification(
                referral.referrer,
            ).schedule(days_ahead=3)
