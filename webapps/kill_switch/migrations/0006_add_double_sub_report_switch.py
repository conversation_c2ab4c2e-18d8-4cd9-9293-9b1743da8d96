# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2019-06-10 14:30
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('kill_switch', '0005_add_more_switches'),
    ]

    operations = [
        migrations.AlterField(
            model_name='killswitch',
            name='name',
            field=models.CharField(
                choices=[
                    ('business_availability', 'Business Availability'),
                    ('c2b_referral', 'C2B Referral'),
                    ('double_subscriptions_report', 'Double subscriptions report'),
                    ('google_reserve_live_updates', 'Google reserve live updates'),
                    ('yelp_reserve_live_updates', 'Yelp reserve live updates'),
                    ('pipedrive_celery', 'Pipedrive celery updates'),
                    ('slots_from_slave', 'Use Slave DB for time slots handler'),
                    ('slave_booking_listing', 'Use Slave DB for customer bookings list handler'),
                ],
                max_length=32,
                unique=True,
            ),
        ),
    ]
