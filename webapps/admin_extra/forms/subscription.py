from django import forms
from django.db import transaction

from lib.enums import BaseEnum
from lib.tools import tznow
from webapps.admin_extra.forms.mixins import (
    DryRunFormMixin,
    ImportFileFormMixin,
)
from webapps.admin_extra.import_tools.enums import (
    SubscriptionsEditFields,
    SubscriptionsImportFields,
)
from webapps.admin_extra.widgets import AdminPlusSplitDateTime
from webapps.business.models import Business
from webapps.purchase.models import (
    Subscription,
    SubscriptionBusinessDiscount,
    SubscriptionListing,
)
from webapps.purchase.tasks import ComputeBusinessStatusTask


class AddSubscriptionForm(forms.Form):
    """DEPRECATED"""

    business_id = forms.IntegerField(
        label='Business ID',
        min_value=1,
        widget=forms.TextInput(),
        error_messages={'invalid': 'Invalid Business ID'},
    )
    # Product
    product = forms.ModelChoiceField(
        queryset=SubscriptionListing.objects.filter(
            active=True,
            braintree_id='',
            apple_id='',
            google_id='',
        ),
    )

    # Product preview
    product_price_amount = forms.CharField(
        disabled=True,
        required=False,
        label='Product price',
    )
    product_discount = forms.CharField(
        disabled=True,
        required=False,
        label='Product default discount',
    )
    product_payment_time_in_months = forms.CharField(
        disabled=True,
        required=False,
        label='Payment period (in months)',
    )
    product_min_staff = forms.CharField(
        disabled=True,
        required=False,
        label='Min staff',
    )
    product_max_staff = forms.CharField(
        disabled=True,
        required=False,
        label='Max staff',
    )
    product_sms_limit = forms.CharField(
        disabled=True,
        required=False,
        label='Sms limit',
    )

    # Subscription
    start = forms.SplitDateTimeField(
        widget=AdminPlusSplitDateTime(),
        label='Subscription start',
    )
    expiry = forms.SplitDateTimeField(
        required=False,
        widget=AdminPlusSplitDateTime,
        label='Subscription expiry',
    )

    # Discount
    discount_type = forms.CharField(
        disabled=True,
        required=False,
        initial='Initial',
    )
    discount_from = forms.SplitDateTimeField(
        widget=AdminPlusSplitDateTime(),
        required=False,
    )
    discount_to = forms.SplitDateTimeField(
        widget=AdminPlusSplitDateTime(),
        required=False,
    )
    discount_percentage = forms.DecimalField(
        max_digits=3,
        decimal_places=0,
        label='Discount %',
        min_value=-100,
        max_value=100,
        required=False,
    )

    def __init__(self, **kwargs):
        self.user = kwargs.pop('user')
        self.admin_view = kwargs.pop('admin_view')
        super().__init__(**kwargs)

    def clean_business_id(self):
        business_id = self.cleaned_data['business_id']
        if not Business.objects.filter(id=business_id).exists():
            self.add_error('business_id', forms.ValidationError('Business does not exist'))
        return business_id

    def clean(self):
        discount_from = self.cleaned_data.get('discount_from')
        discount_to = self.cleaned_data.get('discount_to')
        discount_percentage = self.cleaned_data.get('discount_percentage')
        if discount_percentage and (not discount_from or not discount_to):
            raise forms.ValidationError('Please set discount period.')

    def save(self):
        with transaction.atomic():
            product = self.cleaned_data['product']
            sub = Subscription.objects.create(
                business_id=self.cleaned_data['business_id'],
                product=product,
                start=self.cleaned_data['start'],
                expiry=self.cleaned_data['expiry'],
                source=Business.PaymentSource.OFFLINE,
            )
            discount_percentage = self.cleaned_data.get('discount_percentage')
            if discount_percentage:
                discount = SubscriptionBusinessDiscount(
                    subscription=sub,
                    discount_from=self.cleaned_data['discount_from'],
                    discount_to=self.cleaned_data['discount_to'],
                    discount_type=SubscriptionBusinessDiscount.DISCOUNT_TYPE__INITIAL,
                )
                if discount_percentage is not None:
                    discount.discount_percentage = discount_percentage
                elif product.discount_percentage is not None:
                    discount.discount_percentage = product.discount_percentage
                else:
                    discount.discount_amount = product.discount_amount
                discount.save()

        metadata = {
            'user_email': self.user.email if self.user else '',
            'user_id': self.user.id if self.user else '',
            'admin_view': self.admin_view,
        }
        business = Business.objects.get(id=self.cleaned_data['business_id'])
        business_update_fields = []
        if business.payment_source == Business.PaymentSource.UNKNOWN:
            business.payment_source = Business.PaymentSource.OFFLINE
            business_update_fields.append('payment_source')
        # We do not support copying sms limit for subscriptions active from
        # different date than today
        if sub.start.date() == tznow().date() and product.postpaid_sms_limit is not None:
            business.sms_limit = product.postpaid_sms_limit
            business_update_fields.append('sms_limit')
        if business_update_fields:
            business.save(update_fields=business_update_fields)

        ComputeBusinessStatusTask.run(
            business_id=sub.business_id,
            metadata=metadata,
        )
        return sub.id


class ImportOfflineForm(forms.Form):
    transfer_date = forms.CharField(
        max_length=50,
        label='Column name for transfer date',
        widget=forms.TextInput(attrs={'placeholder': 'date'}),
        initial='date',
    )
    amount = forms.CharField(
        max_length=50,
        label='Column name for amount',
        widget=forms.TextInput(attrs={'placeholder': 'price'}),
        initial='price',
    )
    external_id = forms.CharField(
        max_length=50,
        label='Column name for external id',
        help_text='Maximum length of external_id value in .xlsx cell is 32 characters.',
        widget=forms.TextInput(attrs={'placeholder': 'external id'}),
        initial='external id',
    )
    payment_method = forms.CharField(
        max_length=50,
        label='Column name for payment method',
        help_text='Maximum length of payment_method value in .xlsx cell is 100 characters.',
        widget=forms.TextInput(attrs={'placeholder': 'payment method'}),
        initial='payment method',
    )
    reference_month = forms.CharField(
        max_length=50,
        label='Column name for reference month',
        help_text='Maximum length of reference_month value in .xlsx cell is 100 characters.',
        widget=forms.TextInput(attrs={'placeholder': 'reference month'}),
        initial='reference month',
    )
    subscription_id = forms.CharField(
        max_length=50,
        label='Column name for subscription id',
        widget=forms.TextInput(attrs={'placeholder': 'subscription id'}),
        initial='subscription id',
    )
    import_file = forms.FileField(label='Spreadsheet')
    email = forms.EmailField(
        label='Email for report',
    )

    key_map_fields = (
        'transfer_date',
        'amount',
        'external_id',
        'payment_method',
        'reference_month',
        'subscription_id',
    )

    def clean(self):
        key_map = {f: self.cleaned_data.get(f) for f in self.key_map_fields}
        self.cleaned_data['key_map'] = key_map
        return self.cleaned_data


class _SubscriptionImportBaseForm(ImportFileFormMixin, DryRunFormMixin, forms.Form):
    FIELDS: BaseEnum

    email = forms.EmailField(
        label='Email for report',
        required=False,
    )


class SubscriptionImportForm(_SubscriptionImportBaseForm):
    FIELDS = SubscriptionsImportFields


class SubscriptionEditForm(_SubscriptionImportBaseForm):
    FIELDS = SubscriptionsEditFields


class AppleSubscriptionsCheckForm(forms.Form):
    number_of_months = forms.IntegerField(min_value=1, max_value=12)
    email = forms.EmailField(label='Email for report')
