from typing import (
    List,
    Tuple,
)

from botocore.exceptions import ClientError
from django.contrib.messages.views import SuccessMessageMixin
from django.forms import Form
from django.http import (
    HttpResponseRedirect,
    JsonResponse,
)
from django.shortcuts import get_object_or_404
from django.urls import reverse

from lib.celery_tools import BaseCeleryTask
from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    View,
)
from webapps.admin_extra.forms.subscription import (
    AddSubscriptionForm,
    AppleSubscriptionsCheckForm,
    ImportOfflineForm,
    SubscriptionEditForm,
    SubscriptionImportForm,
)
from webapps.admin_extra.tasks import (
    subscription_batch_edit_task,
    subscription_import_task,
)
from webapps.admin_extra.views.braintree import (
    SubscriptionSuperPermissionsMixin,
)
from webapps.admin_extra.views.utils import (
    CeleryFileTaskMixin,
    ImportFileMixin,
)
from webapps.purchase.models import (
    Subscription,
    SubscriptionDiscount,
    SubscriptionExternalInfo,
    SubscriptionListing,
)
from webapps.purchase.serializers import SubscriptionListingPreviewSerializer
from webapps.purchase.tasks.apple import apple_subscriptions_check_task
from webapps.purchase.tasks.offline import import_offline_transactions_task
from webapps.user.tools import get_user_from_django_request


class SubscriptionListingView(View):
    """
    Returns subscription product details.
    product_id is required param.
    Probably DEPRECATED.
    """

    permission_required = ()

    def get(self, request, *_args, **_kwargs):
        subscription_product = get_object_or_404(
            SubscriptionListing, id=request.GET.get('product_id')
        )
        data = SubscriptionListingPreviewSerializer(instance=subscription_product).data
        return JsonResponse(data)


class AddSubscriptionView(SuccessMessageMixin, FormView):
    """
    Creates offline subscription for single merchant
    (separate view, can be done from another section in admin panel).
    DEPRECATED
    """

    permission_required = ()
    form_class = AddSubscriptionForm
    template_name = 'admin/custom_views/add_subscription.html'
    success_message = 'Success!'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = get_user_from_django_request(self.request)
        kwargs['admin_view'] = 'AddSubscriptionView'
        return kwargs

    def form_valid(self, form):
        subscription_id = form.save()
        return HttpResponseRedirect(
            reverse('admin:purchase_subscription_change', args=(subscription_id,)),
        )


class ImportOfflineView(CeleryFileTaskMixin, FormView):
    """
    Importer for offline subscription transactions.
    """

    permission_required = (
        'purchase.add_subscription',
        'purchase.change_subscription',
    )
    template_name = 'admin/custom_views/generic_form_template.html'
    form_class = ImportOfflineForm
    success_url = 'import'
    import_file_names = ('import_file',)
    import_directory = 'offline'
    celery_task = import_offline_transactions_task
    celery_task_kwargs = ('key_map', 'email')
    success_message = 'Please wait up to 15 mins for result.'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Import offline transactions'
        context['save_button_title'] = 'Submit'
        return context


class SubscriptionImportBaseView(
    SubscriptionSuperPermissionsMixin,
    ImportFileMixin,
    SuccessMessageMixin,
    FormView,
):
    form_class: Form
    template_name: str
    success_url: str
    template_file_name: str
    example_file_name: str
    import_task: BaseCeleryTask
    import_file_names = ('import_file',)
    import_directory = 'subscriptions'
    permission_required = ()

    @classmethod
    def get_example_files(cls):
        return [
            {
                'param': 'download_template',
                'path': f'statics/xlsx/import_subscriptions_example/{cls.template_file_name}',
                'mime': consts.XLS_MIME,
            },
            {
                'param': 'download_example',
                'path': f'statics/xlsx/import_subscriptions_example/{cls.example_file_name}',
                'mime': consts.XLS_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        instalments = Subscription.InstalmentType.choices()
        discounts = SubscriptionDiscount.DiscountType.choices()
        recovers = SubscriptionExternalInfo.VindicationFormChoices.choices()
        context['instalment_types'] = self._get_choice_names(instalments)
        context['recover_choices'] = self._get_choice_names(recovers)
        context['discount_types'] = self._get_choice_names(discounts)
        context['fields'] = ['Messages'] + self.form_class.FIELDS.values()
        return context

    @staticmethod
    def _get_choice_names(choices: List[Tuple[str, str]]) -> List[str]:
        return [name.lower() for _, name in choices]

    def form_valid(self, form):
        cleaned_data = form.cleaned_data
        try:
            file_path = self.save_file('import_file', form)
        except ClientError:
            form.add_error('import_file', 'Unable to upload file to S3')
            return self.form_invalid(form)

        context = self.get_context_data(form=form)

        task_kwargs = {
            'file_path': file_path,
            'report_email': cleaned_data.get('email'),
            'user_id': get_user_from_django_request(self.request).id,
        }

        if form.cleaned_data['dry_run']:
            data = self.import_task(**task_kwargs, dry_run=True)
            context.update({'data': data})
            return self.render_to_response(context)

        self.import_task.delay(**task_kwargs)
        return super().form_valid(form)


class SubscriptionImportView(SubscriptionImportBaseView):
    """
    Offline subscription import.
    """

    form_class = SubscriptionImportForm
    template_name = 'admin/custom_views/subscription_import.html'
    success_url = 'subscription_import'
    template_file_name = 'subscriptions_template.xlsx'
    example_file_name = 'subscriptions_example.xlsx'
    import_task = subscription_import_task


class SubscriptionBatchEditView(SubscriptionImportBaseView):
    form_class = SubscriptionEditForm
    template_name = 'admin/custom_views/subscription_batch_edit.html'
    success_url = 'subscription_batch_edit'
    template_file_name = 'subscriptions_edit_template.xlsx'
    example_file_name = 'subscriptions_edit_example.xlsx'
    import_task = subscription_batch_edit_task


class AppleSubscriptionsCheckView(
    SubscriptionSuperPermissionsMixin,
    SuccessMessageMixin,
    FormView,
):
    """
    #71729
    Verifies Apple subscriptions' expiry dates and compares them with Business
    status. If merchant has paid but is on Payment Overdue Active (POA) or
    Payment Overdue Blocked (POB) status, Business  will be automatically
    switched to Paid status. If merchant hasn't paid and is on PO/POB status,
    Business id goes to .xlsx spreadsheet and is sent to requested email
    address.
    """

    form_class = AppleSubscriptionsCheckForm
    template_name = 'admin/custom_views/generic_form_template.html'
    success_message = 'Email with results will be sent up to 20 min.'
    success_url = 'apple_subscription_check'
    permission_required = ()

    def form_valid(self, form):
        apple_subscriptions_check_task.delay(
            months=form.cleaned_data['number_of_months'],
            email=form.cleaned_data['email'],
        )
        return super().form_valid(form)
