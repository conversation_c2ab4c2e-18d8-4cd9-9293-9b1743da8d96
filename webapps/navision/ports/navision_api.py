import typing as t
from dataclasses import InitVar, dataclass, field
from datetime import date, datetime
from decimal import Decimal
from uuid import uuid4

from dateutil.parser import isoparse
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.functional import cached_property

from country_config import Country
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.navision import (
    UseNavisionMerchantAPIClientV2Flag,
)
from lib.serializers import safe_get
from webapps.navision.enums import (
    InvoicePaymentSource,
    InvoiceService,
    NavisionSource,
    TaxRateService,
)


class InsufficientDataError(ValidationError):
    pass


@dataclass
class _MerchantCommonData:
    """Common fields in both DB and API."""

    tax_id: t.Optional[str] = None
    entity_name: str = None
    address_details1: t.Optional[str] = None
    city: t.Optional[str] = None
    zip_code: t.Optional[str] = None
    state: t.Optional[str] = None
    payment_due_days: t.Optional[int] = 7  # nav_offline
    accounting_group: t.Optional[str] = settings.NAVISION_ACCOUNTING_GROUP


@dataclass
class _MerchantEmailsData:
    """Used for proper field order (non-default first)."""

    emails: InitVar[t.Union[str, t.List[str]]]
    invoice_emails: t.Optional[t.List[str]] = field(default=None, init=False)

    def __post_init__(
        self,
        emails: t.Union[str, t.List[str]],
    ):
        self.invoice_emails = self._get_invoice_emails(emails)

    @staticmethod
    def _get_invoice_emails(emails: t.Union[str, t.List[str]]) -> list:
        if isinstance(emails, str):
            return [emails]
        return emails


@dataclass
class MerchantData(_MerchantCommonData, _MerchantEmailsData):
    """
    Data structure used to create/update a merchant in the database.

    Use it like this:

    data = MerchantData(
        tax_id="**********",
        entity_name="Entity Ltd.",
        emails="<EMAIL>",  # or ["<EMAIL>", "<EMAIL>"]
        address_details1="al. Róż 1",
        city="Warszawa",
        zip_code="01-100",
        state="mazowieckie",
        country_code="pl",
        payment_due_days=14,
        accounting_group='eu',
    )
    """

    invoice_emails: t.Optional[t.List[str]] = field(default=None, init=False)
    country_code: t.Optional[str] = None

    def __post_init__(  # pylint: disable=arguments-differ
        self,
        emails: t.Union[str, t.List[str]],
    ) -> None:
        if self.tax_id:
            for arg, value in [
                ("emails", emails),
                ("country_code", self.country_code),
            ]:
                if not value:
                    raise InsufficientDataError(f"'{arg}' is required for a tax Merchant.")
        else:
            for arg, value in [
                ("address_details1", self.address_details1),
                ("zip_code", self.zip_code),
                ("country_code", self.country_code),
                ("city", self.city),
                ("emails", emails),
            ]:
                if not value:
                    raise InsufficientDataError(f"'{arg}' is required for a non-tax Merchant.")
        if not self.entity_name or self.entity_name.isspace():
            raise InsufficientDataError('Entity name is required.')

        super().__post_init__(emails)


@dataclass
class _MerchantAPIDataBase:
    merchant_id: str
    country_code: str
    price_with_vat: bool


@dataclass
class MerchantNavisionAPIData(_MerchantCommonData, _MerchantAPIDataBase):
    """
    Use it like this:

    data = MerchantNavisionAPIData(
        merchant_id="BC-PL-100003",
        entity_name="Nazwa biznesu",
        invoice_emails=["<EMAIL>", "<EMAIL>"],
        accounting_group="domestic",
        tax_id="*********",
        price_with_vat=True,
        address_details1="al. Róż 1",
        city="Warszawa",
        zip_code="01-100",
        state="mazowieckie",
        country_code="pl",
    )
    """

    invoice_emails: t.Optional[t.List[str]] = None
    owned_businesses: t.Optional[t.List[int]] = None

    @classmethod
    def from_merchant(cls, merchant):
        return cls(
            merchant_id=merchant.merchant_id,
            payment_due_days=merchant.payment_due_days,
            entity_name=merchant.entity_name,
            invoice_emails=merchant.invoice_emails,
            accounting_group=merchant.accounting_group,
            tax_id=merchant.tax_id,
            price_with_vat=merchant.price_with_vat,
            address_details1=merchant.address_details1,
            city=merchant.city,
            zip_code=merchant.zip_code,
            state=merchant.state,
            country_code=merchant.country_code,
            owned_businesses=merchant.owned_businesses_ids,
        )

    @cached_property
    def payload(self) -> dict:  # pylint: disable=invalid-overridden-method
        data = {
            "merchant_id": self.merchant_id,
            "business_name": self.entity_name,
            "email": ("; ".join(self.invoice_emails) if self.invoice_emails else None),
            "accounting_group": self.accounting_group.upper(),
            "tax_id": self.tax_id,
            "price_with_vat": self.price_with_vat,
            "payment_terms_code": str(self.payment_due_days),
        }

        if settings.NAVISION_MERCHANT_BUSINESS_ID_SUPPORTED:
            data['business_id'] = ''
            if self.owned_businesses:
                owned_businesses = ' '.join(
                    str(business_id) for business_id in self.owned_businesses
                )
                if len(owned_businesses) <= 250:
                    data['business_id'] = owned_businesses

        billing_address = self.get_billing_address()
        user_data = UserData(
            subject_key=self.merchant_id, is_experiment=True, subject_type='merchant_id'
        )
        if billing_address:
            if (
                settings.NAVISION_FLAT_MERCHANT_BILLING_ADDRESS
                and UseNavisionMerchantAPIClientV2Flag(user_data)
            ):
                data.update(billing_address)
            else:
                data["billingAddress"] = billing_address
        return data

    def get_billing_address(self) -> t.Optional[dict]:
        from webapps.navision.utils import normalize

        if all([self.address_details1, self.zip_code, self.country_code, self.city]):
            address_details = {
                "address_details1": normalize(self.address_details1, 100),
                "city": normalize(self.city, 30),
                "zip_code": self.zip_code,
                "state": self.state,
                "country_code": self.country_code.upper(),
            }
            if not settings.NAVISION_API_COUNTRY_CODE_SUPPORTED:
                address_details.pop('country_code')
            return address_details


# pylint: disable=too-many-instance-attributes
@dataclass
class _InvoiceCommonData:
    payment_due_date: date  # nav_offline
    sales_date: date
    invoice_date: date
    source: str
    currency: str
    document_type: str
    service: t.Optional[str] = None
    bank_code: t.Optional[str] = None
    business_id: t.Optional[str] = None
    business_name: t.Optional[str] = None
    invoice_note: t.Optional[str] = None
    emails: InitVar[t.Optional[t.Union[str, t.List[str]]]] = None
    invoice_emails: t.Optional[t.List[str]] = field(default=None, init=False)
    aggregation_uuid: t.Optional[uuid4] = None

    def __post_init__(
        self,
        emails: t.Union[str, t.List[str]],
    ):
        self.invoice_emails = self._get_invoice_emails(emails)

    @staticmethod
    def _get_invoice_emails(emails: t.Union[str, t.List[str]]) -> list:
        if isinstance(emails, str):
            return [emails]
        return emails


@dataclass
class _InvoiceDataBase:
    merchant_id: int
    invoice_items: t.List['InvoiceItemData']


@dataclass
class InvoiceData(_InvoiceCommonData, _InvoiceDataBase):
    """
    Use it like this:

    data = InvoiceData(
        payment_due_date=date(2021, 1, 2),
        sales_date=date(2021, 1, 2),
        source="offline",
        currency="USD",
        document_type="VAT",
        invoice_date=date(2021, 1, 2),
        invoice_note="Uwagi ...",  # optional
        emails="<EMAIL>",  # optional (or ["<EMAIL>", "<EMAIL>"])
        aggregation_id=2,
        approved=False,
    )
    """

    approved: bool = False


@dataclass
class _InvoiceItemDataBase:  # pylint: disable=too-many-instance-attributes
    product: str
    service: str
    base_gross_value: Decimal
    billing_cycle_end: datetime
    billing_cycle_start: datetime
    quantity: int = 1
    charging_dt: t.Optional[datetime] = None
    discount_gross_value: t.Optional[Decimal] = None
    base_tax_value: t.Optional[Decimal] = None
    discount_tax_value: t.Optional[Decimal] = None  # nav_offline
    tax_rate: t.Optional[t.Any] = None
    transaction_id: t.Optional[str] = None
    transfer_dt: t.Optional[datetime] = None  # nav_offline
    charge_completed: t.Optional[bool] = None


@dataclass
class InvoiceItemData(_InvoiceItemDataBase):
    """
    Use it like this:

    data = InvoiceItemData(
        product="Example SaaS service line",
        service="SaaS",
        charge_completed=True,
        update_status=InvoiceItem.Status.INIT,  # optional
        base_gross_value=Decimal(10.34),
        billing_cycle_end=datetime(2021, 1, 31, 12, 13, 14),
        billing_cycle_start=datetime(2021, 1, 31, 12, 13, 14),
        currency_code='USD',  # optional, by default taken from settings
        charging_dt=datetime(2021, 1, 31, 12, 13, 14),  # optional (None if charge_completed=False)
        discount_gross_value=Decimal(2),  # optional
        transaction_id="*********",  # optional
        transfer_dt=datetime(2021, 1, 31, 12, 13, 14),  # optional
        invoiced_object=SubscriptionTransaction(...),  # optional
        tax_additional_data=TaxAdditionalInfo(
            tax_rate_id=1,
            area='zip',
            tax_area_name='90210',
        ), # optional, only for US, default None
    )
    """

    product_identifier: t.Optional[str] = None
    payment_source: t.Optional[InvoicePaymentSource] = None
    invoiced_object: t.Optional[t.Any] = None
    # We want to track currency of every item, having invoice with
    # mixed currencies would be really awful
    currency_code: str = settings.CURRENCY_CODE
    update_status: t.Optional[str] = None
    tax_additional_data: t.Optional[dict] = None


@dataclass
class _InvoiceItemAPIDataBase:
    booksy_item_id: str


@dataclass
class _InvoiceAPIDataBase:
    merchant_id: str
    booksy_id: str
    invoice_items: t.List["InvoiceItemAPIData"]
    aggregation_total: t.Optional[str]


@dataclass  # pylint: disable=too-many-instance-attributes
class InvoiceItemAPIData(_InvoiceItemDataBase, _InvoiceItemAPIDataBase):
    """
    Use it like this:

    data = InvoiceItemAPIData(
        booksy_item_id="US-123",
        product="Example SaaS service line",
        service="SaaS",
        charge_completed=True,
        country_code='us',  # optional, by default taken from settings
        base_gross_value=Decimal(10.34),
        billing_cycle_end=datetime(2021, 1, 31, 12, 13, 14),
        billing_cycle_start=datetime(2021, 1, 31, 12, 13, 14),
        charging_dt=datetime(2021, 1, 31, 12, 13, 14),  # optional
        transaction_id="*********",  # optional
        transfer_dt=datetime(2021, 1, 31, 12, 13, 14),  # optional
        tax_area_code='90210',  # only for US, default None
    )
    """

    country_code: str = settings.NAVISION_COUNTRY
    tax_area_code: t.Optional[str] = None


@dataclass  # pylint: disable=too-many-instance-attributes
class InvoiceNavisionAPIBase:
    """
    Base data structure used to move an invoice data to external API.

    Inheriting class must add `payload` implementation which returns data
    structure specific to API system it uses.
    """

    @property
    def payload(self):
        raise NotImplementedError

    @classmethod
    def _cast_to_float(cls, value: t.Optional[t.Union[Decimal, int, str]]) -> t.Optional[float]:
        return float(value) if isinstance(value, (Decimal, str)) and value != "" else value

    @classmethod
    def _dt_iso(
        cls,
        value: t.Optional[t.Union[date, datetime]],
    ) -> t.Optional[str]:
        return value.isoformat() if isinstance(value, (date, datetime)) else value


@dataclass
class InvoiceNavisionAPIData(InvoiceNavisionAPIBase, _InvoiceCommonData, _InvoiceAPIDataBase):
    """
    Use it like this:

    data = InvoiceNavisionAPIData(
        sales_tax_date='12-04-2002',  # only for US, default None
        merchant_id="BC-PL-100003",
        payment_due_date=date(2021, 1, 2),
        sales_date=date(2021, 1, 2),
        source="offline",
        currency="USD",
        document_type="VAT",
        invoice_date=date(2021, 1, 2),
        invoice_note="Uwagi ...",  # optional
        emails="<EMAIL>",  # optional (or ["<EMAIL>", "<EMAIL>"])
        invoice_items=[
            InvoiceItemAPIData(
                booksy_item_id="US-123",
                product="Example SaaS service line",
                service="SaaS",
                charge_completed=True,
                base_gross_value=Decimal(10.34),
                billing_cycle_end=datetime(2021, 1, 31, 12, 13, 14),
                billing_cycle_start=datetime(2021, 1, 31, 12, 13, 14),
                charging_dt=datetime(2021, 1, 31, 12, 13, 14),  # optional
                transaction_id="*********",  # optional
                transfer_dt=datetime(2021, 1, 31, 12, 13, 14),  # optional
            ),
        ],
    )
    """

    sales_tax_date: t.Optional[date] = None

    @staticmethod
    def get_sales_tax_date(invoice_items: 'InvoiceItem') -> t.Optional[str]:
        if settings.NAVISION_ENABLE_TAX_RATE_INTEGRATION:
            last_transaction_item = (
                invoice_items.exclude(charging_dt__isnull=True)
                .only('charging_dt')
                .values('charging_dt')
                .order_by('-charging_dt')
                .first()
            )
            return last_transaction_item['charging_dt'].date() if last_transaction_item else None

    @staticmethod
    def get_tax_area_code(invoice_item: 'InvoiceItem', merchant_zip_code: str) -> t.Optional[str]:
        if settings.NAVISION_ENABLE_TAX_RATE_INTEGRATION:
            return (
                safe_get(invoice_item, ['tax_additional_data', 'tax_area_name'])
                or merchant_zip_code
            )

    @staticmethod
    def get_source_name(source: 'InvoicePaymentSource', service: 'Invoice.Service') -> str:
        navision_source = NavisionSource.from_invoice_payment_source(source)
        return f'{navision_source}_{service}'

    @staticmethod
    def get_service_for_invoice_item(tax_id_exists, service):
        if settings.API_COUNTRY == Country.IE and not tax_id_exists:
            service = f'{service}_EU'
        return service

    @classmethod
    def from_invoice(cls, invoice):
        from webapps.navision.models import Invoice

        invoice_items = invoice.items.all()
        zip_code = safe_get(invoice, ['merchant', 'zip_code'])

        if invoice.aggregation_uuid is not None:
            aggregation_total = Invoice.objects.filter(
                aggregation_uuid=invoice.aggregation_uuid
            ).count()
        else:
            aggregation_total = None

        if invoice_items and not invoice_items.exclude(service=Invoice.Service.SMS).exists():
            service = Invoice.Service.SMS
        else:
            service = invoice.service

        tax_id_exists = bool(invoice.merchant.tax_id)

        return cls(
            merchant_id=invoice.merchant.merchant_id,
            booksy_id=invoice.booksy_id,
            payment_due_date=invoice.payment_due_date,
            sales_date=invoice.sales_date,
            bank_code=invoice.bank_code,
            business_id=invoice.business_id,
            business_name=invoice.business_name,
            source=cls.get_source_name(invoice.source, service),
            service=service,
            currency=invoice.currency,
            document_type=invoice.document_type,
            invoice_date=invoice.invoice_date,
            invoice_note=invoice.invoice_note,
            emails=invoice.invoice_emails,
            aggregation_uuid=invoice.aggregation_uuid,
            aggregation_total=aggregation_total,
            sales_tax_date=cls.get_sales_tax_date(invoice_items),
            invoice_items=[
                InvoiceItemAPIData(
                    booksy_item_id=invoice_item.booksy_item_id,
                    product=invoice_item.product,
                    charge_completed=invoice_item.charge_completed,
                    quantity=invoice_item.quantity,
                    service=cls.get_service_for_invoice_item(tax_id_exists, invoice_item.service),
                    base_gross_value=invoice_item.base_gross_value,
                    billing_cycle_end=invoice_item.billing_cycle_end,
                    billing_cycle_start=invoice_item.billing_cycle_start,
                    charging_dt=invoice_item.charging_dt,
                    discount_gross_value=invoice_item.discount_gross_value,
                    transaction_id=invoice_item.transaction_id,
                    transfer_dt=invoice_item.transfer_dt,
                    tax_area_code=cls.get_tax_area_code(invoice_item, zip_code),
                )
                for invoice_item in invoice_items
            ],
        )

    @cached_property
    def payload(self) -> dict:  # pylint: disable=invalid-overridden-method
        data = {
            'sales_tax_date': self._dt_iso(self.sales_tax_date),
            'merchant_id': self.merchant_id,
            'booksy_id': self.booksy_id,
            'payment_due_date': self._dt_iso(self.payment_due_date),
            'sales_date': self._dt_iso(self.sales_date),
            'source': self.source,
            'currency': self.currency,
            'service_type': self.service,
            'document_type': self.document_type,
            'invoice_date': self._dt_iso(self.invoice_date),
            'bank_code': self.bank_code,
            'business_name': self.business_name,
            'email': ('; '.join(self.invoice_emails) if self.invoice_emails else None),
            'invoice_items': [self.get_invoice_item(item) for item in self.invoice_items],
            'business_id': str(self.business_id) if self.business_id is not None else None,
        }

        user_data = UserData(
            subject_key=self.merchant_id, is_experiment=True, subject_type='merchant_id'
        )
        if (
            UseNavisionMerchantAPIClientV2Flag(user_data)
            or not settings.NAVISION_SALES_DATE_SUPPORTED
        ):
            data.pop('sales_date')

        if (
            UseNavisionMerchantAPIClientV2Flag(user_data)
            or not settings.NAVISION_BANK_CODE_SUPPORTED
        ):
            data.pop('bank_code')

        if settings.NAVISION_AGGREGATE_INVOICES and self.aggregation_uuid is not None:
            data['aggregation_id'] = str(self.aggregation_uuid)
            data['aggregation_total'] = self.aggregation_total

        return data

    def get_invoice_item(self, item: InvoiceItemAPIData) -> dict:
        base_gross_value = item.base_gross_value

        if item.discount_gross_value:
            base_gross_value -= item.discount_gross_value

        if item.service.lower().startswith(InvoiceService.DISCOUNT.lower()):
            base_gross_value = -abs(base_gross_value)

        return {
            'tax_area_code': item.tax_area_code,
            'booksy_item_id': item.booksy_item_id,
            'country_code': item.country_code,
            'base_gross_value': self._cast_to_float(base_gross_value),
            'billing_cycle_end': self._dt_iso(item.billing_cycle_end),
            'billing_cycle_start': self._dt_iso(item.billing_cycle_start),
            'charging_dt': self._dt_iso(item.charging_dt) if item.charge_completed else None,
            'product': item.product,
            'quantity': item.quantity,
            'service': item.service,
            'charge_completed': item.charge_completed,
            'transaction_id': item.transaction_id,
            'transfer_dt': self._dt_iso(item.transfer_dt),
        }

    @classmethod
    def from_unsaved_hof_invoice(cls, invoice: 'Invoice', invoice_items: list['InvoiceItem']):
        from webapps.navision.models import Invoice

        if invoice_items and all(item.service == Invoice.Service.SMS for item in invoice_items):
            service = Invoice.Service.SMS
        else:
            service = invoice.service

        return cls(
            merchant_id=invoice.merchant.merchant_id,
            booksy_id=str(uuid4()).replace('-', '')[:30],
            payment_due_date=invoice.payment_due_date.date(),
            sales_date=invoice.sales_date.date(),
            bank_code=invoice.bank_code,
            business_id=invoice.business_id,
            business_name=invoice.business_name,
            source=cls.get_source_name(invoice.source, service),
            service=service,
            currency=invoice.currency,
            document_type=invoice.document_type,
            invoice_date=invoice.invoice_date.date(),
            invoice_note=invoice.invoice_note,
            emails=invoice.invoice_emails,
            aggregation_uuid=invoice.aggregation_uuid,
            aggregation_total=None,
            sales_tax_date=None,
            invoice_items=[
                InvoiceItemAPIData(
                    booksy_item_id=f'{idx}',
                    product=invoice_item.product,
                    charge_completed=invoice_item.charge_completed,
                    quantity=invoice_item.quantity,
                    service=invoice_item.service,
                    base_gross_value=invoice_item.base_gross_value,
                    billing_cycle_end=invoice_item.billing_cycle_end,
                    billing_cycle_start=invoice_item.billing_cycle_start,
                    charging_dt=invoice_item.charging_dt,
                    discount_gross_value=invoice_item.discount_gross_value,
                    transaction_id=invoice_item.transaction_id,
                    transfer_dt=invoice_item.transfer_dt,
                    tax_area_code=None,
                )
                for idx, invoice_item in enumerate(invoice_items, start=1)
            ],
        )


@dataclass
class InvoiceItemUpdatedNavisionAPIData(_InvoiceItemAPIDataBase):
    """
    Base data structure used to move an invoice data to external API.

    Use it like this:

    data = InvoiceItemUpdatedNavisionAPIData(
        booksy_item_id="US-123",
        charge_completed=True,
        transaction_id="*********",
        charging_dt=datetime(2021, 1, 31, 12, 13, 14),
    )
    """

    charge_completed: t.Optional[bool] = None
    transaction_id: t.Optional[str] = None
    charging_dt: t.Optional[datetime] = None


@dataclass
class InvoiceUpdatedNavisionAPIData(InvoiceNavisionAPIBase):
    """
    Base data structure used to move an invoice data to external API.

    Use it like this:

    data = InvoiceUpdatedNavisionAPIData(
        booksy_id="PL-100003",
        charge_line=[
            InvoiceItemUpdatedNavisionAPIData(
                booksy_item_id="US-123",
                charge_completed=True,
                transaction_id="*********",
                charging_dt=datetime(2021, 1, 31, 12, 13, 14),
            ),
        ],
    )
    """

    booksy_id: str
    charge_line: list['InvoiceItemUpdatedNavisionAPIData']

    @classmethod
    def from_invoice(cls, invoice):
        invoice_items = invoice.items.all()

        return cls(
            booksy_id=invoice.booksy_id,
            charge_line=[
                InvoiceItemUpdatedNavisionAPIData(
                    booksy_item_id=invoice_item.booksy_item_id,
                    charge_completed=invoice_item.charge_completed,
                    transaction_id=invoice_item.transaction_id,
                    charging_dt=invoice_item.charging_dt,
                )
                for invoice_item in invoice_items
            ],
        )

    @cached_property
    def payload(self) -> dict:  # pylint: disable=invalid-overridden-method
        return {
            'booksy_id': self.booksy_id,
            'charge_line': [self.get_invoice_item(item) for item in self.charge_line],
        }

    def get_invoice_item(self, item: InvoiceItemUpdatedNavisionAPIData) -> dict:
        return {
            'booksy_item_id': item.booksy_item_id,
            'charge_completed': item.charge_completed,
            'transaction_id': item.transaction_id,
            'charging_dt': self._dt_iso(item.charging_dt) if item.charge_completed else None,
        }


@dataclass
class TaxRateItemAPIData:
    service_type: TaxRateService
    tax_rate: Decimal

    @classmethod
    def from_api_response(cls, response: t.Dict) -> 'TaxRateItemAPIData':
        return cls(
            service_type=response['serviceType'],
            tax_rate=Decimal(str(response['taxRate'])) / 100,
        )


@dataclass
class TaxRateAPIData:
    zip_code: str
    last_modified: datetime
    items: t.List[TaxRateItemAPIData]

    @classmethod
    def from_api_response(cls, response: t.Dict) -> 'TaxRateAPIData':
        last_modified = isoparse(response['zip2TaxLastModifiedDateTime'])

        items = [
            TaxRateItemAPIData.from_api_response(item_dict) for item_dict in response['tax_items']
        ]

        return cls(
            zip_code=response['code'],
            last_modified=last_modified,
            items=items,
        )
