import logging

from webapps.business.v2.public import business_rwg_service, BusinessRWGDTO
from webapps.google_business_profile.domain.const import InternalCategory
from webapps.google_business_profile.domain.services.postal_code import PostalCodeService
from webapps.google_business_profile.domain.interfaces.business_gateway import BusinessGateway
from webapps.google_business_profile.domain.models import Business
from webapps.google_business_profile.domain.value_objects import (
    BusinessCategory,
    LocationCategory,
    LocationAddress,
    BusinessIdentity,
    BusinessSupplementaryDetails,
    BusinessTemporalInfo,
    BusinessGbpDetails,
    BusinessPrimaryContact,
    BusinessGeoDetails,
    BooksyCategory,
)
from webapps.google_business_profile.domain.value_types import (
    RegionCode,
    LanguageCode,
    CountryCode,
    Email,
)
from webapps.google_business_profile.shared import (
    BusinessId,
    BusinessStatus,
    BookingMode,
    UserId,
    WebsiteUri,
    PhoneNumber,
)

logger = logging.getLogger('booksy.google_business_profile')


class BusinessDetailsGateway(BusinessGateway):
    @staticmethod
    def _convert_rwg_to_business_data(rwg_dto: BusinessRWGDTO, business_id: BusinessId) -> Business:
        address = LocationAddress(
            region_code=RegionCode(rwg_dto.country_code.upper()),
            language_code=LanguageCode('en-US'),  # TODO !!! do it like BoostConditionsView.get
            postal_code=PostalCodeService.create_postal_code(rwg_dto.zipcode, rwg_dto.country_code),
            administrative_area=rwg_dto.region_state_code,  # TODO !!! not always follow ISO 3166-2
            locality=rwg_dto.city,
            address_lines=[rwg_dto.address],
        )

        google_location_category = LocationCategory(
            name=rwg_dto.primary_category_slug,
            category_id=str(rwg_dto.primary_category_id),
            display_name=rwg_dto.primary_category_name,
        )

        identity = BusinessIdentity(
            name=rwg_dto.name,
            official_name=rwg_dto.official_name,
        )
        supplementary_details = BusinessSupplementaryDetails(
            description=rwg_dto.description,
            renting_venue=rwg_dto.renting_venue,
            subdomain=rwg_dto.subdomain,
        )
        temporal_info = BusinessTemporalInfo(
            created=rwg_dto.created,
            updated=rwg_dto.updated,
            visible_from=rwg_dto.visible_from,
            active_from=rwg_dto.active_from,
            active_till=rwg_dto.active_till,
        )
        gbp_details = BusinessGbpDetails(
            synchronised_gbp=False,
            has_gbp=False,
            disable_google_reserve=rwg_dto.disable_google_reserve,
            verification=rwg_dto.verification,
        )
        primary_contact = BusinessPrimaryContact(
            phone=PhoneNumber(rwg_dto.phone),
            website=WebsiteUri(rwg_dto.website) if rwg_dto.website else None,
            email=Email(rwg_dto.public_email) if rwg_dto.public_email else None,
        )
        geo_details = BusinessGeoDetails(
            longitude=rwg_dto.longitude,
            latitude=rwg_dto.latitude,
            address2=rwg_dto.address2,
            city_or_region_city=rwg_dto.city_or_region_city,
            region_id=rwg_dto.region_id,
        )

        booksy_category = BooksyCategory(
            id=rwg_dto.primary_category_id,
            name=rwg_dto.primary_category_name,
            slug=rwg_dto.primary_category_slug,
        )

        return Business(
            business_id=business_id,
            country_code=CountryCode(rwg_dto.country_code.upper()),
            owner_id=UserId(rwg_dto.owner_id),
            identity=identity,
            supplementary_details=supplementary_details,
            temporal_info=temporal_info,
            gbp_details=gbp_details,
            primary_contact=primary_contact,
            geo_details=geo_details,
            address=address,
            category=google_location_category,
            booksy_category=booksy_category,
            booking_mode=BookingMode(rwg_dto.booking_mode),
            status=BusinessStatus(rwg_dto.status),
            active=rwg_dto.active,
            visible=rwg_dto.visible,
        )

    @staticmethod
    def _convert_to_business_address_to_check(rwg_dto: BusinessRWGDTO) -> LocationAddress:
        return LocationAddress(
            region_code=RegionCode(rwg_dto.country_code.upper()),
            language_code=LanguageCode('en-US'),  # TODO !!! do it like BoostConditionsView.get
            postal_code=PostalCodeService.create_postal_code(rwg_dto.zipcode, rwg_dto.country_code),
            address_lines=[
                rwg_dto.address,
                rwg_dto.city,
                rwg_dto.region_state_code,
            ],
        )

    @staticmethod
    def _fetch_business_rwg_data(business_id: BusinessId) -> BusinessRWGDTO | None:
        try:
            return business_rwg_service.get_business_rwg_data(business_id)
        except ValueError as e:
            logger.error("Error fetching business details for business_id=%s: %s", business_id, e)
            return None

    @staticmethod
    def _fetch_business_category_data(business_id: BusinessId) -> BusinessCategory | None:
        try:
            category_data = business_rwg_service.get_business_category_rwg_data(business_id)
            return BusinessCategory(
                category_id=category_data.primary_category_id,
                name=category_data.primary_category_name,
                slug=category_data.primary_category_slug,
                report_name=category_data.primary_category_report_name,
            )
        except ValueError as e:
            logger.error("Error fetching business category: %s", e)
            return None

    def get_business_details(self, business_id: BusinessId) -> Business | None:
        business_details = self._fetch_business_rwg_data(business_id)
        if not business_details:
            logger.warning("No business details found for business_id=%s", business_id)
            return None

        return self._convert_rwg_to_business_data(business_details, business_id)

    def get_business_address_to_check(self, business_id: BusinessId) -> LocationAddress:
        business_details = self._fetch_business_rwg_data(business_id)

        if not business_details:
            logger.error(
                "Cannot create address to check - no business details for business_id=%s",
                business_id,
            )
            raise ValueError(f"No business details found for business_id={business_id}")

        business_address = self._convert_to_business_address_to_check(business_details)

        return business_address

    def get_business_primary_category(self, business_id: BusinessId) -> BusinessCategory | None:
        return self._fetch_business_category_data(business_id)

    def map_to_google_category(self, category: BusinessCategory) -> LocationCategory:
        internal_category = InternalCategory.from_report_name(category.report_name)
        google_category = Business.map_to_google_category(internal_category)

        logger.info("Mapped %s to Google category %s", internal_category.value, google_category)

        return google_category
