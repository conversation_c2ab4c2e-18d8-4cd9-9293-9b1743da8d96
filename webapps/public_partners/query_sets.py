from django.db.models import Prefetch

from lib.feature_flag.feature.public_api import PublicAPIPartnerAppMetadataExposedFlag


class PartnerAppDataQuerySet:
    def with_partner_app_data(self, metadata_class):
        if PublicAPIPartnerAppMetadataExposedFlag():
            return self.prefetch_related(
                Prefetch(
                    'partner_app_data',
                    queryset=metadata_class.objects.select_related('application').filter(
                        application__is_active=True,
                        deleted__isnull=True,
                    ),
                )
            )
        return self
