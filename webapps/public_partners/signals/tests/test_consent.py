from model_bakery import baker

from lib.tools import tznow
from webapps.business.baker_recipes import bci_recipe
from webapps.consents.models import Consent
from webapps.public_partners.checksums import ConsentChecksumSerializer
from webapps.public_partners.enum import WebhookEnum
from webapps.public_partners.models import ConsentChecksum
from webapps.public_partners.signals import (
    send_consent_webhooks_to_partner_apps_signal,
)
from webapps.public_partners.signals.tests import (
    BaseWebhookSignalV04TestCase,
    WebhookSignalConditionsMixin,
)


class BaseConsentWebhookSignalV04TestCase(BaseWebhookSignalV04TestCase):
    MODEL = Consent
    CHECKSUM_MODEL = ConsentChecksum

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.bci = bci_recipe.make(business=cls.business)
        cls.consent = baker.make(Consent, customer=cls.bci, form_title='Foo')

    def _current_signal(self, sender=None, instance=None, **kwargs):
        return send_consent_webhooks_to_partner_apps_signal(
            sender=sender or self.MODEL,
            instance=instance or self.consent,
            **kwargs,
        )


class ConsentCreateWebhookSignalV04TestCase(
    WebhookSignalConditionsMixin, BaseConsentWebhookSignalV04TestCase
):
    EVENT = WebhookEnum.CONSENT_CREATED

    def test_signal_with_consent_after_create(self):
        self.assert_checksum_changes(changes=1, sender=Consent, instance=self.consent, created=True)
        self.assert_schedule_webhooks_task(
            checksum=self.CHECKSUM_MODEL.objects.filter(entity=self.consent)
            .order_by('-created')
            .first(),
        )


class ConsentUpdateWebhookSignalV04TestCase(
    WebhookSignalConditionsMixin, BaseConsentWebhookSignalV04TestCase
):
    EVENT = WebhookEnum.CONSENT_UPDATED

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.consent_checksum = baker.make(
            cls.CHECKSUM_MODEL,
            entity=cls.consent,
            checksum=ConsentChecksumSerializer(cls.consent).checksum,
        )

    def test_signal_with_consent_after_update(self):
        self.consent.signed = tznow()
        self.consent.save(update_fields=['signed'])
        self.assert_checksum_changes(changes=1, sender=Consent, instance=self.consent)
        self.assert_schedule_webhooks_task(
            checksum=self.CHECKSUM_MODEL.objects.filter(entity=self.consent)
            .order_by('-created')
            .first(),
        )

    def test_signal_with_consent_after_update_and_no_changes(self):
        self.consent.form_title = 'New title'
        self.consent.save(update_fields=['form_title'])
        self.assert_checksum_changes(changes=0, sender=Consent, instance=self.consent)
        self.assert_not_schedule_webhooks_task()

    def test_signal_with_consent_after_update_and_not_tracked(self):
        self.consent.updated = tznow()  # not tracked
        self.consent.save(update_fields=['updated'])
        self.assert_checksum_changes(changes=0, sender=Consent, instance=self.consent)
        self.assert_not_schedule_webhooks_task()


class ConsentDeleteWebhookSignalV04TestCase(
    WebhookSignalConditionsMixin, BaseConsentWebhookSignalV04TestCase
):
    EVENT = WebhookEnum.CONSENT_DELETED

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.consent_checksum = baker.make(
            cls.CHECKSUM_MODEL,
            entity=cls.consent,
            checksum=ConsentChecksumSerializer(cls.consent).checksum,
        )

    def test_signal_with_consent_after_soft_delete(self):
        self.consent.deleted = tznow()
        self.consent.save(update_fields=['deleted'])
        self.assert_checksum_changes(changes=1, sender=Consent, instance=self.consent)
        self.assert_schedule_webhooks_task(
            checksum=self.CHECKSUM_MODEL.objects.filter(entity=self.consent)
            .order_by('-created')
            .first(),
        )
