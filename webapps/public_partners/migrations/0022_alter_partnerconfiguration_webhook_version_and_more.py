# Generated by Django 4.1.7 on 2023-05-22 08:42

from django.db import migrations, models
import webapps.public_partners.enum


class Migration(migrations.Migration):
    dependencies = [
        ('public_partners', '0021_alter_oauth2application_authorization_grant_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='partnerconfiguration',
            name='webhook_version',
            field=models.CharField(
                choices=[
                    ('0.1', 'Version 0.1'),
                    ('0.2', 'Version 0.2'),
                    ('0.3', 'Version 0.3'),
                    ('0.4', 'Version 0.4'),
                    ('0.5', 'Version 0.5'),
                ],
                default=webapps.public_partners.enum.PublicAPIVersionEnum['V01'],
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name='webhook',
            name='payload_version',
            field=models.CharField(
                blank=True,
                choices=[('0.4', 'Version 0.4'), ('0.5', 'Version 0.5')],
                max_length=10,
                null=True,
            ),
        ),
    ]
