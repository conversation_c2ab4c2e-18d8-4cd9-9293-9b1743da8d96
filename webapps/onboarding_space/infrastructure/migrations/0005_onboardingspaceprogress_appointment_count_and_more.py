# Generated by Django 4.2.23 on 2025-07-14 11:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('onboarding_space', '0004_alter_onboardingspaceprogress_portfolio_count_and_more'),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name='onboardingspaceprogress',
            name='appointment_count',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='onboardingspaceprogress',
            name='business_primary_category_internal_name',
            field=models.CharField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='onboardingspaceprogress',
            name='portfolio',
            field=models.BooleanField(default=False, null=True),
        ),
        migrations.AlterField(
            model_name='onboardingspaceprogress',
            name='services',
            field=models.BooleanField(default=False, null=True),
        ),
    ]
