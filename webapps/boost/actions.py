import typing as t

from lib.events import event_receiver, async_event_receiver
from lib.tools import tznow
from webapps.boost.boost_reactivation import (
    boost_should_be_enabled,
    enable_boost_for_business,
    boost_was_planned_to_be_deactivated,
)
from webapps.boost.events import (
    stripe_app_payment_failed,
    stripe_app_payment_required_action,
    stripe_app_payment_succeed,
    stripe_app_refund_charge,
    marketplace_transaction_successfully_paid,
)
from webapps.boost.payment.stripe import PaymentStripe
from webapps.business.models import Business, BusinessPromotion
from webapps.marketplace.tasks import set_has_braintree_task


@event_receiver(stripe_app_payment_failed)
def stripe_payment_failed_handler(
    sender: t.Any,  # pylint: disable=unused-argument
    result: dict,
    **kwargs,
) -> None:
    PaymentStripe.refresh_transaction(result=result)


@event_receiver(stripe_app_payment_required_action)
def stripe_payment_action_required_handler(
    sender: t.Any,  # pylint: disable=unused-argument
    result: dict,
    **kwargs,
) -> None:
    PaymentStripe.refresh_transaction(result=result)


@event_receiver(stripe_app_payment_succeed)
def stripe_payment_succeed_handler(
    sender: t.Any,  # pylint: disable=unused-argument
    result: dict,
    **kwargs,
) -> None:
    PaymentStripe.refresh_transaction(result=result)


@event_receiver(stripe_app_refund_charge)
def stripe_refund_charge_handler(
    sender: t.Any,  # pylint: disable=unused-argument
    result: dict,
    **kwargs,
) -> None:
    PaymentStripe.refresh_refund(result=result)


# @event_receiver(stripe_app_refund_update)
# def stripe_refund_update_handler(
#     sender: t.Any,  # pylint: disable=unused-argument
#     result: dict,
#     **kwargs,
# ) -> None:
#     PaymentStripe.refresh_refund(result=result)


@async_event_receiver(marketplace_transaction_successfully_paid)
def boost_auto_reactivation_handler(
    sender: t.Any,  # pylint: disable=unused-argument
    business_id: int,
    **kwargs,
) -> None:
    set_has_braintree_task.run(
        business_id=business_id,
        metadata={'event': 'marketplace_transaction_successfully_paid()'},
    )  # the Px successfully paid, hence the Px has online method working

    business_with_disabled_boost = Business.objects.filter(
        id=business_id, boost_status=Business.BoostStatus.DISABLED
    ).first()

    if business_with_disabled_boost and boost_should_be_enabled(business_with_disabled_boost):
        if boost_was_planned_to_be_deactivated(business_with_disabled_boost):
            business_with_disabled_boost.marketplacebusiness.clear_dates(
                deactivation=True,
                history_params={'metadata': {'event': 'marketplace_transaction_successfully_paid'}},
            )

            now = tznow()
            commission_id = (
                BusinessPromotion.objects.filter(business=business_with_disabled_boost)
                .order_by('-promotion_start')
                .values_list('commission', flat=True)
                .first()
                # probably there is a commission, but None is also ok
            )
            BusinessPromotion.objects.create(
                business=business_with_disabled_boost,
                promotion_start=now,
                promotion_end=now,
                commission_id=commission_id,
                enabled_by=BusinessPromotion.BY_SYSTEM,
                disabled_by=BusinessPromotion.BY_SYSTEM,
            )
        else:
            enable_boost_for_business(business_with_disabled_boost)
