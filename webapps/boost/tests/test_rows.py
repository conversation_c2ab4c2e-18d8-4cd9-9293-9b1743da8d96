from mock import patch, Mock

from django.test import TestCase
from model_bakery import baker

from lib.tools import tznow
from webapps.booking.models import SubBooking
from webapps.boost.baker_recipes import boosted_business_recipe
from webapps.boost.payment.braintree import PaymentBraintree
from webapps.boost.tests.utils import create_boost_finished_visit
from webapps.marketplace.tasks import (
    pay_marketplace_chunk_transaction_task,
    mass_set_chargeable_payable_task,
)
from webapps.marketplace.tests import get_dummy_transaction_response
from webapps.pos.baker_recipes import receipt_recipe, transaction_recipe
from webapps.pos.enums import receipt_status
from webapps.pos.models import Transaction, TransactionRow


@patch.object(PaymentBraintree, 'pay', Mock(return_value=(None, get_dummy_transaction_response())))
class TestAddingRemovingSubbookings(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = boosted_business_recipe.make()

    def setUp(self) -> None:
        self.appointment = create_boost_finished_visit(
            business=self.business,
            subbookings=[{}] * 3,
        )
        self.boost_appointment = self.appointment.boost_appointment
        assert self.boost_appointment.rows.count() == 3

        self.transaction = self.boost_appointment.transaction

    def make_pos_payment(self):
        txn = transaction_recipe.make(
            appointment=self.appointment,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = receipt_recipe.make(
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        for sbk in self.appointment.bookings.all():
            baker.make(
                TransactionRow,
                transaction=txn,
                subbooking=sbk,
                discounted_total=10.00,  # so that 10% of it is 1.00
            )

    def add_booking(self):
        new_subbooking = baker.prepare(SubBooking, appointment=self.appointment)
        new_subbooking.save(override=True)

    def delete_booking(self):
        removed_booking = self.appointment.bookings.last()
        removed_booking.deleted = tznow()
        removed_booking.save(override=True)

    def assert_changes_reflected(self, expected_num):
        self.boost_appointment.refresh_from_db()
        self.assertEqual(
            expected_num,
            self.appointment.bookings.filter(payable=True).count(),
            'num payable bookings',
        )
        self.assertEqual(expected_num, self.boost_appointment.rows.count(), 'num of rows')
        self.assertEqual(expected_num, self.transaction.payable_rows.count(), 'payable rows in txn')
        self.assertEqual(1.00 * expected_num, self.boost_appointment.amount, 'summed BA amount')

    def assert_no_changes(self):
        self.transaction.refresh_from_db()
        self.boost_appointment.update_amount()

        self.assert_changes_reflected(3)

    def test_subbooking_added_before_payment(self):
        self.add_booking()

        self.transaction.pay()

        self.assert_changes_reflected(4)

    def test_subbooking_deleted_before_payment(self):
        self.delete_booking()

        self.transaction.pay()

        self.assert_changes_reflected(2)

    def test_subbooking_added_after_payment(self):
        pay_marketplace_chunk_transaction_task.run([self.transaction.id])

        self.add_booking()

        self.assert_no_changes()

    def test_subbooking_deleted_after_payment(self):
        pay_marketplace_chunk_transaction_task.run([self.transaction.id])

        self.delete_booking()

        self.assert_no_changes()

    def test_addition_of_subbooking_triggered_the_mass_task(self):
        self.add_booking()

        mass_set_chargeable_payable_task.run()

        self.assert_changes_reflected(4)

    def test_deletion_of_subbooking_triggered_the_mass_task(self):
        self.delete_booking()

        mass_set_chargeable_payable_task.run()

        self.assert_changes_reflected(2)

    def test_settled_by_pos_and_then_extra_added(self):
        """only 3 paid by pos subbookings should be charged, the extra booking should not"""
        self.make_pos_payment()

        self.add_booking()
        self.transaction.pay()

        self.assert_no_changes()
