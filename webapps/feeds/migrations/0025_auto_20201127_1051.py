# Generated by Django 3.1.2 on 2020-11-27 10:51

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0024_autoupdate_queryset_as_manager_67904'),
    ]

    operations = [
        migrations.AlterField(
            model_name='googlebooking',
            name='actions',
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.JSONField(), blank=True, null=True, size=None
            ),
        ),
        migrations.AlterField(
            model_name='googlefeedstarter',
            name='google_feeds_ids',
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name='googlefeedstarter',
            name='google_feeds_parts',
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name='googlefeedstatus',
            name='statistic',
            field=models.JSONField(blank=True),
        ),
        migrations.AlterField(
            model_name='yelpbooking',
            name='fulfillment_raw',
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='yelpfeedstatus',
            name='businesses_results',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='yelporderstatuschangenotification',
            name='coupon',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='yelporderstatuschangenotification',
            name='transaction_status_change',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='yelporderstatuschangenotification',
            name='user_info',
            field=models.JSONField(blank=True, null=True),
        ),
    ]
