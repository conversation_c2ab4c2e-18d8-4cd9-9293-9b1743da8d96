import functools
import hashlib
import hmac
import logging

# defusedxml has a bug https://github.com/tiran/defusedxml/issues/48#issuecomment-1286024777
from xml.etree.ElementTree import Element, SubElement  # nosemgrep

from defusedxml import minidom
from defusedxml.ElementTree import tostring
from django.conf import settings
from django.db.models import Prefetch
from requests import PreparedRequest
from requests.auth import AuthBase
from rest_framework import status
from tornado.httputil import HTTPServerRequest

from webapps.business.enums import PriceType
from webapps.business.models import Business, Service, ServiceVariant
from webapps.feeds.facebook.config import SERVICE_IMG_URL
from webapps.feeds.utils import facebook_external_link

logger = logging.getLogger('booksy.facebook.fbe.tools')


def verify_sha1(key: str = settings.FB_APP_SECRET):
    """
    Decorator for RequestHandler verifying Facebook signature.
    """

    def decor(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            req = self.request
            base_api_url = req.protocol + "://" + req.host + req.uri
            logger.info(
                "verifier, class name: %s, base url: %s, headers: %s, arguments: %s",
                self.__class__.__name__,
                base_api_url,
                dict(self.request.headers),
                self.request.arguments,
            )
            if verify_fb_signature(self.request, api_key=key):
                return method(self, *args, **kwargs)
            logger.info("signature NOT verified")
            return self.return_error(
                'Invalid signature',
                status.HTTP_401_UNAUTHORIZED,
            )

        return wrapper

    return decor


def verify_fb_signature(request: HTTPServerRequest, api_key: str) -> bool:
    payload = request.body or b''
    signature = request.headers.get('x-hub-signature', '')
    expected_signature = calculate_signature(payload, api_key)
    logger.info("Expected signature: %s", expected_signature)
    logger.info("Received signature: %s", signature)
    return hmac.compare_digest(expected_signature, signature)


def calculate_signature(payload: bytes, api_key: str) -> str:
    logger.debug("Signed payload: %s", payload)
    cleaned_payload = (
        payload.replace(b"/", b"\\/")
        .replace(b"@", b"\\u0040")
        .replace(b"%", b"\\u0025")
        .replace(b"<", b"\\u003C")
    )
    sha1_value = hmac.new(api_key.encode(), cleaned_payload, digestmod=hashlib.sha1).hexdigest()
    return f"sha1={sha1_value}"


class FacebookFBEAuth(AuthBase):
    """Attaches HTTP Authentication header to the given Request object."""

    def __init__(self, secret: str = settings.FB_APP_SECRET) -> None:
        self.secret = secret

    def __call__(self, request: PreparedRequest) -> PreparedRequest:
        body = request.body
        signature = calculate_signature(body, self.secret)
        request.headers['X-Hub-Signature'] = signature
        return request


def build_services_xml_for_fb(business: Business) -> bytes:
    service_variant_query = (
        ServiceVariant.objects.filter(active=True)
        .only(
            'duration',
            'id',
            'price',
            'service',
            'type',
        )
        .order_by('duration')
    )
    services = (
        Service.objects.filter(
            business=business,
            active=True,
            deleted__isnull=True,
        )
        .prefetch_related(
            Prefetch('service_variants', queryset=service_variant_query, to_attr='active_variants'),
        )
        .only(
            'business_id',
            'description',
            'id',
            'name',
            'padding_type',
        )
        .order_by('created')
    )
    root = Element('listings')
    title = SubElement(root, 'title')
    title.text = f'FBE services feed for {business.name}'
    SubElement(
        root,
        'link',
        {'rel': 'self', 'href': business.get_seo_url()},
    )
    for idx, service in enumerate(services, 1):
        _append_service_info(idx, service, root, business)
    return _prettify(root)


price_type_mapping = {
    PriceType.FIXED: 'VALUE',
    PriceType.FREE: 'FREE',
    PriceType.STARTS_AT: 'MINIMUM',
    PriceType.VARIES: 'VARIES',
    PriceType.DONT_SHOW: 'TIME_DEPENDENT',  # the closest there is
}


def _append_service_info(sidx: int, service: Service, root: Element, business: Business) -> None:
    for vidx, variant in enumerate(service.active_variants):
        listing = SubElement(root, 'listing')
        SubElement(listing, 'item_id').text = str(variant.id)
        SubElement(listing, 'title').text = service.name
        SubElement(listing, 'description').text = service.description
        SubElement(listing, 'price_type').text = price_type_mapping.get(variant.type, 'VALUE')
        if variant.type and PriceType(variant.type, None) in PriceType.has_price():
            SubElement(listing, 'price').text = f'{variant.price:.2f} {settings.CURRENCY_CODE}'
        SubElement(listing, 'duration_time').text = str(variant.duration.minutes)
        SubElement(listing, 'duration_type').text = 'FIXED'
        SubElement(listing, 'url').text = facebook_external_link(service.business_id, variant.id)

        before_start = '0'
        after_end = '0'
        if (padding_type := service.padding_type) and (padding_time := service.padding_time):
            duration = str(padding_time.minutes)
            if padding_type == Service.PADDING_AFTER:
                after_end = duration
            elif padding_type == Service.PADDING_BEFORE:
                before_start = duration
            else:
                before_start = after_end = duration
        SubElement(listing, 'time_padding_before_start').text = before_start
        SubElement(listing, 'time_padding_after_end').text = after_end

        image = SubElement(listing, 'image')
        SubElement(image, 'url').text = SERVICE_IMG_URL
        SubElement(image, 'tag').text = f'{business.name} - {service.name}'
        SubElement(listing, 'order_index').text = str(sidx * 1000 + vidx)


def _prettify(element: Element) -> bytes:
    """Return a pretty-printed XML bytes for the xml.Element"""
    rough_string = tostring(element, 'unicode')
    parsed = minidom.parseString(rough_string)
    return parsed.toprettyxml(indent="  ", encoding='utf-8')
