from unittest.mock import patch

# pylint: disable=no-name-in-module
from booksy_proto_notifications_webhooks.pubsub.v1.webhook_pb2 import (
    NotificationStatus,
    NotificationWebhook,
    ServiceProvider,
)

# pylint: enable=no-name-in-module

from webapps.notification.enums import NotificationSendStatus
from webapps.notification.subscribers.webhook import WebhooksSubscriber


@patch('webapps.notification.tasks.set_sms_notification_status.delay')
def test_status_success(mock_set_sms_notification_status):
    api_status = 'DELIVERED'
    notification_webhook_message = NotificationWebhook(
        service_provider=ServiceProvider.SMS_TELNYX,
        webhook_id='web_id',
        receiver='48501502503',
        status=NotificationStatus.SUCCESS,
        errors=None,
        payload=[('status', api_status), ('x', 'x')],
    )

    WebhooksSubscriber().handle(data=notification_webhook_message)

    mock_set_sms_notification_status.assert_called_once_with(
        webhook_id='web_id',
        send_status=NotificationSendStatus.WEBHOOK_SUCCESS,
        api_status=api_status,
        phone_number='+48501502503',
        errors=None,
    )


@patch('webapps.notification.tasks.set_sms_notification_status.delay')
def test_status_failed(mock_set_sms_notification_status):
    notification_webhook_message = NotificationWebhook(
        service_provider=ServiceProvider.SMS_TELNYX,
        webhook_id='some_id',
        receiver='48501502503',
        status=NotificationStatus.ERROR,
        errors=[('error1', 'error_value1'), ('error_2', 'error_value2')],
    )

    WebhooksSubscriber().handle(data=notification_webhook_message)

    mock_set_sms_notification_status.assert_called_once_with(
        webhook_id='some_id',
        send_status=NotificationSendStatus.WEBHOOK_ERROR,
        api_status=None,
        phone_number='+48501502503',
        errors=[
            {
                'error1': 'error_value1',
                'error_2': 'error_value2',
            }
        ],
    )


@patch('webapps.notification.tasks.set_sms_notification_status.delay')
def test_status_skipped(
    mock_set_sms_notification_status,
):
    notification_webhook_message = NotificationWebhook(
        service_provider=ServiceProvider.SMS_TELNYX,
        webhook_id='some_id',
        status=NotificationStatus.OTHER,
    )

    WebhooksSubscriber().handle(data=notification_webhook_message)

    assert mock_set_sms_notification_status.call_count == 0
