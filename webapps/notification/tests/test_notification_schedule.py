import datetime
import json
from unittest.mock import Mock

from django.test import Test<PERSON>ase
import pytest
from mock import patch
from model_bakery import baker

import settings
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.booking.notifications.recipients import AppointmentCustomer
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import CustomData
from webapps.notification.base import BaseNotification
from webapps.notification.channels import Email<PERSON>hannel
from webapps.notification.enums import NotificationCategory, ScheduleState
from webapps.notification.models import NotificationSchedule
from webapps.notification.recipients import SystemSender
from webapps.notification.scenarios.scenarios_booking import (
    BookingChangedScenario,
    CustomerBookingReminderType,
)
from webapps.notification.tasks import execute_task, process_notification_schedules_task_with_cache


@pytest.mark.django_db
@patch('webapps.notification.scenarios.dispatch_scenario_task')
def test_process_notification_schedule_task(dispatch_mock):
    dispatch_mock.return_value = ''

    past = baker.make(
        NotificationSchedule,
        scheduled=tznow() - datetime.timedelta(seconds=60),
        state=ScheduleState.PENDING,
        parameters={},
    )
    future = baker.make(
        NotificationSchedule,
        scheduled=tznow() + datetime.timedelta(seconds=60),
        state=ScheduleState.PENDING,
        parameters={},
    )

    process_notification_schedules_task_with_cache.run()
    past.refresh_from_db()
    future.refresh_from_db()

    # `past` was taken from the queue and processed
    assert past.state == ScheduleState.SUCCESS

    # `future` was not modified
    assert future.state == ScheduleState.PENDING


class ExceptionForTest(Exception):
    pass


@pytest.mark.django_db
@patch(
    'webapps.notification.scenarios.scenarios_booking.BookingFinishedScenario.event_next_day',
    side_effect=ExceptionForTest('mocked error'),
)
def test_scenario_notification_failure_raises(event_next_day_mock):
    task = baker.make(
        NotificationSchedule,
        task_id='booking_finished:next_day:appointment_id=123',
        parameters={},
    )

    with pytest.raises(ExceptionForTest):
        execute_task(task)

    task.refresh_from_db()
    assert task.state == ScheduleState.FAILURE
    assert json.loads(task.result)[-1]['exception'] == "ExceptionForTest('mocked error')"


@pytest.mark.django_db
def test_new_notification_failure_raises():
    # pylint: disable=protected-access
    prev_registry = BaseNotification._registry
    BaseNotification._registry = {}

    class TestNotification(BaseNotification):  # pylint: disable=unused-variable
        recipients = (AppointmentCustomer,)
        channels = (EmailChannel,)
        sender = SystemSender
        category = NotificationCategory.BOOKING_CHANGED

        def __init__(self, instance, **kwargs):
            super().__init__(instance, **kwargs)
            raise ExceptionForTest('something went wrong')

    task = baker.make(
        NotificationSchedule,
        task_id='TestNotification, 1597760',
        parameters={},
    )
    with pytest.raises(ExceptionForTest):
        execute_task(task)

    task.refresh_from_db()
    assert task.state == ScheduleState.FAILURE
    assert json.loads(task.result)[-1]['exception'] == "ExceptionForTest('something went wrong')"

    BaseNotification._registry = prev_registry


@pytest.mark.freeze_time(datetime.datetime(2022, 8, 2, 12, 30))
@patch(
    'webapps.notification.scenarios.scenarios_booking.BookingChangedScenario.get_reminder_hours',
    return_value=24,
)
class TestBookingChangedScenarioReminder:
    def test_is_customer_booking_reminder_needed(self, get_reminder_hours_mock):
        """Test that by default notifications will be sent out up to 18 hours before
        the appointment is scheduled to start.
        """
        mocked_appointment = Mock(
            type=Appointment.TYPE.BUSINESS,
            status=Appointment.STATUS.ACCEPTED,
        )

        mocked_appointment.booked_from = tznow() + datetime.timedelta(hours=18)
        assert BookingChangedScenario.is_booking_reminder_needed(mocked_appointment) is True

        # should NOT allow notification as this is one second past allowed time window
        mocked_appointment.booked_from = tznow() + datetime.timedelta(
            hours=17,
            minutes=59,
            seconds=59,
        )
        assert BookingChangedScenario.is_booking_reminder_needed(mocked_appointment) is False

    def test_is_additional_customer_booking_reminder_needed(self, get_reminder_hours_mock):
        mocked_appointment = Mock(
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.ACCEPTED,
        )

        mocked_appointment.booked_from = tznow() + datetime.timedelta(
            hours=BookingChangedScenario.MIN_REMINDER_HOURS
        )
        assert (
            BookingChangedScenario.is_booking_reminder_needed(
                mocked_appointment, reminder_type=CustomerBookingReminderType.HOURS_BEFORE
            )
            is True
        )

        # should NOT allow notification as this is one second past allowed time window
        mocked_appointment.booked_from = tznow() - datetime.timedelta(seconds=1)
        assert (
            BookingChangedScenario.is_booking_reminder_needed(
                mocked_appointment, reminder_type=CustomerBookingReminderType.HOURS_BEFORE
            )
            is False
        )

    def test_is_time_diff_acceptable(self, get_reminder_hours_mock):
        mocked_appointment = Mock(updated=tznow())

        # there are exactly 26 hours between `updated` and `booked_from`
        mocked_appointment.booked_from = mocked_appointment.updated + datetime.timedelta(
            hours=24 + 2
        )
        assert BookingChangedScenario.is_time_diff_acceptable(mocked_appointment) is True

        # when we update the appointment 25 hours before the appointment is scheduled to start
        mocked_appointment.updated = mocked_appointment.booked_from - datetime.timedelta(hours=25)

        # then the notification should be skipped
        assert BookingChangedScenario.is_time_diff_acceptable(mocked_appointment) is False


class TestGetReminderHours(TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.business = business_recipe.make()
        self.appointment = create_appointment([{}], business=self.business)

    def test_use_default_option_for_wrong_settings(self):
        self.business.custom_data = {}
        self.business.save()
        self.assertEqual(
            BookingChangedScenario.get_reminder_hours(appointment=self.appointment),
            settings.DEFAULT_REMINDER_HOURS,
        )

    def test_use_business_settings(self):
        self.business.custom_data[CustomData.BOOKING_REMIND_BEFORE] = 72
        self.business.save()

        self.assertEqual(
            BookingChangedScenario.get_reminder_hours(appointment=self.appointment), 72
        )

    def test_use_previous_hours(self):
        self.business.custom_data[CustomData.BOOKING_REMIND_BEFORE] = 72
        key = (self.appointment.created + datetime.timedelta(days=5)).isoformat()
        self.business.custom_data[CustomData.REMINDER_HOUR_CHANGE_OLD_VALUE] = {key: 17}
        self.business.save()
        self.assertEqual(
            BookingChangedScenario.get_reminder_hours(appointment=self.appointment), 17
        )

    def test_use_current_settings_hours(self):
        self.business.custom_data[CustomData.BOOKING_REMIND_BEFORE] = 72
        key = (self.appointment.created + datetime.timedelta(days=5)).isoformat()
        self.business.custom_data[CustomData.REMINDER_HOUR_CHANGE_OLD_VALUE] = {key: 80}
        self.business.save()
        self.assertEqual(
            BookingChangedScenario.get_reminder_hours(appointment=self.appointment), 72
        )
