import unittest
from decimal import Decimal

import pytest
from model_bakery import baker

from webapps.business.models import Business
from webapps.invoicing.models import CustomerInvoice, InvoiceItem
from webapps.invoicing.serializers.invoice_item import InvoiceItemSerializer


@pytest.mark.django_db
class InvoiceItemsTests(unittest.TestCase):
    serializer_class = InvoiceItemSerializer

    def setUp(self):
        super().setUp()

        business = baker.make(Business)
        self.invoice = baker.make(CustomerInvoice, business=business)

        self.valid_data = {
            "invoice": self.invoice.id,
            "name": "Some commodity",
            "quantity": 2,
            "unit_symbol": "szt.",
            "net_unit_price": "10.00",
            "tax_rate": "23.00",
        }

    def test_is_valid(self):
        serializer = self.serializer_class(data=self.valid_data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # Empty name is invalid
        data = self.valid_data.copy()
        data['name'] = ''
        self.assertFalse(self.serializer_class(data=data).is_valid())

        # Empty quantity is invalid
        data = self.valid_data.copy()
        data['quantity'] = ''
        self.assertFalse(self.serializer_class(data=data).is_valid())
        # Negative quantity should be invalid
        data['quantity'] = -1
        self.assertFalse(self.serializer_class(data=data).is_valid())

        # Empty unit_symbol is invalid
        data = self.valid_data.copy()
        data['unit_symbol'] = ''
        self.assertFalse(self.serializer_class(data=data).is_valid())

        # Empty net_unit_price is invalid
        data = self.valid_data.copy()
        data['net_unit_price'] = ''
        self.assertFalse(self.serializer_class(data=data).is_valid())
        # Negative quantity net_unit_price be invalid
        data['net_unit_price'] = '-10.00'
        self.assertFalse(self.serializer_class(data=data).is_valid())

        # Empty tax_rate is treated like null tax_rate
        data = self.valid_data.copy()
        data['tax_rate'] = ''
        self.assertTrue(self.serializer_class(data=data).is_valid())
        # Tax rate cannot be negative
        data['tax_rate'] = '-23.00'
        self.assertFalse(self.serializer_class(data=data).is_valid())
        # Tax rate may be null
        data['tax_rate'] = None
        serializer = self.serializer_class(data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        # Tax rate may be zero
        data['tax_rate'] = '0.00'
        serializer = self.serializer_class(data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

    def test_create(self):
        serializer = self.serializer_class(data=self.valid_data)
        self.assertTrue(serializer.is_valid())
        serializer.save()

        invoice_item = InvoiceItem.objects.first()
        self.assertIsNotNone(invoice_item)
        self.assertEqual(invoice_item.invoice.id, self.valid_data['invoice'])
        self.assertEqual(invoice_item.name, self.valid_data['name'])
        self.assertEqual(invoice_item.quantity, self.valid_data['quantity'])
        self.assertEqual(invoice_item.unit_symbol, self.valid_data['unit_symbol'])
        self.assertEqual(invoice_item.tax_rate, Decimal(self.valid_data['tax_rate']))

    def test_update(self):
        invoice_item = baker.make(InvoiceItem, invoice=self.invoice)
        serializer = self.serializer_class(
            instance=invoice_item,
            data=self.valid_data,
        )
        self.assertTrue(serializer.is_valid())
        serializer.save()

        invoice_item.refresh_from_db()
        self.assertEqual(invoice_item.invoice.id, self.valid_data['invoice'])
        self.assertEqual(invoice_item.name, self.valid_data['name'])
        self.assertEqual(invoice_item.quantity, self.valid_data['quantity'])
        self.assertEqual(invoice_item.unit_symbol, self.valid_data['unit_symbol'])
        self.assertEqual(invoice_item.tax_rate, Decimal(self.valid_data['tax_rate']))
