#!/usr/bin/env python
from django.contrib import admin
from django.utils.html import format_html

from lib.admin_helpers import BaseModelAdmin
from webapps.photo.models import Photo, ServicePhoto


class PhotoAdmin(BaseModelAdmin):
    list_display = ('id', 'image_url', 'image_thumbnail')
    readonly_fields = ('link', 'image_display', 'path', 'name', 'created', 'updated', 'deleted')

    def has_add_permission(self, request):
        return False

    @staticmethod
    def link(obj):
        return format_html('<a href="{obj.url}">{obj.url}</a>', obj=obj)

    @staticmethod
    def image_display(obj):
        return format_html('<img src="{}"/>', obj.full_url)

    @staticmethod
    def image_thumbnail(obj):
        return format_html(
            '<img src="{}" style="width: 100px; height: 100px"/>', obj.thumbnail(100, 100)
        )

    @staticmethod
    def image_url(obj):
        return obj.image_url

    image_url.allow_tags = True


admin.site.register(Photo, PhotoAdmin)


class ServicePhotoAdmin(BaseModelAdmin):
    list_display = ('id', 'image_url', 'image_thumbnail', 'order')

    def has_add_permission(self, request):
        return False

    @staticmethod
    def image_thumbnail(obj):
        return format_html(
            '<img src="{}" style="width: 100px; height: 100px"/>', obj.photo.thumbnail(100, 100)
        )

    @staticmethod
    def image_url(obj):
        return obj.photo.image_url

    image_url.allow_tags = True


admin.site.register(ServicePhoto, ServicePhotoAdmin)
