from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal

import stripe
from dataclasses_json import DataClassJsonMixin
from pytz import UTC

from settings.local import StripeAppConfig
from webapps.stripe_app.errors import ErrorObject
from webapps.stripe_app.events import get_registered_event
from webapps.stripe_app.interfaces.refund import StripeRefundInterface
from webapps.stripe_app.models.charge import Dispute, DisputeObject, RefundObject
from webapps.stripe_app.services.charge import PaymentIntentService
from webapps.stripe_app.models import Charge, Refund
from webapps.stripe_app.enums import (
    ErrorType,
    RefundReason,
    ServiceErrorCode,
    StripeEventType,
    Subscriber,
)
from webapps.stripe_app.services.utils import generate_idempotency_key


@dataclass(frozen=True)
class RefundResult(DataClassJsonMixin):
    is_success: bool
    refund: RefundObject = field(default=None)
    error: ErrorObject = field(default=None)


@dataclass(frozen=True)
class DisputeResult(DataClassJsonMixin):
    is_success: bool
    dispute: DisputeObject = field(default=None)
    error: ErrorObject = field(default=None)


class RefundService:
    @staticmethod
    def get(*, stripe_id: str) -> RefundResult:
        refund = Refund.objects.filter(stripe_id=stripe_id).select_related('payment_intent').first()

        if refund:
            return RefundResult(
                is_success=True,
                refund=refund.to_dataclass(),
            )
        return RefundResult(
            is_success=False,
            error=ErrorObject(
                type=ErrorType.SERVICE_ERROR,
                code=ServiceErrorCode.REFUND_DOES_NOT_EXIST.value,
                message=ServiceErrorCode.REFUND_DOES_NOT_EXIST.label,
            ),
        )

    @staticmethod
    def create_from_payment_intent(
        *,
        decimal_amount: Decimal,
        stripe_id: str,
        metadata: dict,
        stripe_config: StripeAppConfig,
        subscriber: Subscriber,
        reason: RefundReason | None = None,
        subscriber_key: str | None = None,
        idempotency_key: str | None = None,
    ) -> RefundResult:
        payment_intent_result = PaymentIntentService.get(stripe_id=stripe_id)

        if not payment_intent_result.is_success:
            return RefundResult(
                is_success=False,
                error=ErrorObject(
                    type=ErrorType.SERVICE_ERROR,
                    code=ServiceErrorCode.PAYMENT_INTENT_DOES_NOT_EXIST.value,
                    message=ServiceErrorCode.PAYMENT_INTENT_DOES_NOT_EXIST.label,
                ),
            )

        match metadata:
            case None:
                metadata = {'subscriber': subscriber}
            case _ if 'subscriber' not in metadata:
                metadata['subscriber'] = subscriber

        idempotency_key = idempotency_key or generate_idempotency_key()
        metadata['idempotency_key'] = idempotency_key
        metadata['subscriber_key'] = subscriber_key
        amount = int(decimal_amount * 100)

        stripe_result = StripeRefundInterface(stripe_config).create_from_payment_intent(
            amount=amount,
            payment_intent_id=stripe_id,
            reason=reason,
            metadata=metadata,
            idempotency_key=idempotency_key,
        )

        if not stripe_result.is_success:
            return RefundResult(
                is_success=False,
                error=stripe_result.error,
            )

        charge = None
        stripe_refund = stripe_result.refund

        if stripe_refund.charge and (
            charge := Charge.objects.filter(stripe_id=stripe_refund.charge.id).first()
        ):
            charge.amount_refunded = stripe_refund.charge.amount_refunded
            charge.refunded = stripe_refund.charge.refunded
            charge.save(update_fields=['amount_refunded', 'refunded'])

        refund, _ = Refund.objects.get_or_create(
            stripe_id=stripe_refund.stripe_id,
            defaults={
                'payment_intent_id': payment_intent_result.payment_intent.id,
                'amount': stripe_refund.amount,
                'decimal_amount': decimal_amount,
                'currency': stripe_refund.currency,
                'status': stripe_refund.status,
                'reason': stripe_refund.reason,
                'charge': charge,
                'balance_transaction': stripe_refund.balance_transaction,
                'created_at': (
                    datetime.fromtimestamp(stripe_refund.created, tz=UTC)
                    if stripe_refund.created
                    else None
                ),
                'failure_reason': getattr(stripe_refund, 'failure_reason', None),
                'next_action': getattr(stripe_refund, 'next_action', None),
                'failure_balance_transaction': getattr(
                    stripe_refund,
                    'failure_balance_transaction',
                    None,
                ),
                'metadata': metadata,
                'subscriber': subscriber,
                'subscriber_key': subscriber_key,
            },
        )

        return RefundResult(
            is_success=True,
            refund=refund.to_dataclass(),
        )

    @staticmethod
    def handle_charge_refunded_data_from_webhook(
        *,
        stripe_charge: stripe.api_resources.charge.Charge,
        event_type: StripeEventType,
    ) -> RefundResult:
        stripe_refund = stripe_charge.refunds.data[0]
        payment_intent_result = PaymentIntentService.get(stripe_id=stripe_refund.payment_intent)

        if not payment_intent_result.is_success:
            return RefundResult(
                is_success=False,
                error=ErrorObject(
                    type=ErrorType.SERVICE_ERROR,
                    code=ServiceErrorCode.PAYMENT_INTENT_DOES_NOT_EXIST.value,
                    message=ServiceErrorCode.PAYMENT_INTENT_DOES_NOT_EXIST.label,
                ),
            )

        metadata = getattr(stripe_refund, 'metadata') or {}

        if charge := Charge.objects.filter(stripe_id=stripe_charge.id).first():
            charge.amount_refunded = stripe_charge.amount_refunded
            charge.refunded = stripe_charge.refunded
            charge.save(update_fields=['amount_refunded', 'refunded'])

        subscriber = metadata.get('subscriber') or payment_intent_result.payment_intent.subscriber

        refund, _ = Refund.objects.get_or_create(
            stripe_id=stripe_refund.id,
            defaults={
                'payment_intent_id': payment_intent_result.payment_intent.id,
                'amount': stripe_refund.amount,
                'decimal_amount': Decimal(stripe_refund.amount) / Decimal('100.0'),
                'currency': stripe_refund.currency,
                'status': stripe_refund.status,
                'reason': stripe_refund.reason,
                'charge': charge,
                'balance_transaction': stripe_refund.balance_transaction,
                'created_at': (
                    datetime.fromtimestamp(stripe_refund.created, tz=UTC)
                    if stripe_refund.created
                    else None
                ),
                'failure_reason': getattr(stripe_refund, 'failure_reason', None),
                'next_action': getattr(stripe_refund, 'next_action', None),
                'failure_balance_transaction': getattr(
                    stripe_refund,
                    'failure_balance_transaction',
                    None,
                ),
                'subscriber': subscriber,
                'metadata': metadata,
                'subscriber_key': metadata.get('subscriber_key')
                or payment_intent_result.payment_intent.subscriber_key,
            },
        )

        result = RefundResult(is_success=True, refund=refund.to_dataclass())

        if event := get_registered_event(
            event_type=event_type,
            subscriber=subscriber,
        ):
            event.send('stripe_app', result=result.to_dict(), event_type=event_type)

        return result

    @staticmethod
    def handle_charge_refund_update_data_from_webhook(
        *,
        stripe_refund: stripe.api_resources.refund.Refund,
        event_type: StripeEventType,
    ) -> RefundResult:
        if not (refund := Refund.objects.filter(stripe_id=stripe_refund.id).first()):
            return RefundResult(
                is_success=False,
                error=ErrorObject(
                    type=ErrorType.SERVICE_ERROR,
                    code=ServiceErrorCode.REFUND_DOES_NOT_EXIST.value,
                    message=ServiceErrorCode.REFUND_DOES_NOT_EXIST.label,
                ),
            )

        refund.status = getattr(stripe_refund, 'status', None)
        refund.failure_reason = getattr(stripe_refund, 'failure_reason', None)
        refund.next_action = getattr(stripe_refund, 'next_action', None)
        refund.save(update_fields=['status', 'failure_reason', 'next_action'])

        result = RefundResult(is_success=True, refund=refund.to_dataclass())

        if event := get_registered_event(
            event_type=event_type,
            subscriber=refund.subscriber,
        ):
            event.send('stripe_app', result=result.to_dict(), event_type=event_type)

        return result


class DisputeService:
    @staticmethod
    def get(*, stripe_id: str) -> DisputeResult:
        dispute = Dispute.objects.filter(stripe_id=stripe_id).first()

        if dispute:
            return DisputeResult(
                is_success=True,
                dispute=dispute.to_dataclass(),
            )
        return DisputeResult(
            is_success=False,
            error=ErrorObject(
                type=ErrorType.SERVICE_ERROR,
                code=ServiceErrorCode.DISPUTE_DOES_NOT_EXIST.value,
                message=ServiceErrorCode.DISPUTE_DOES_NOT_EXIST.label,
            ),
        )

    @staticmethod
    def retrieve_data(stripe_dispute: stripe.api_resources.dispute.Dispute):
        return dict(
            decimal_amount=Decimal(stripe_dispute.amount) / Decimal('100.0'),
            amount=stripe_dispute.amount,
            currency=stripe_dispute.currency,
            status=stripe_dispute.status,
            reason=stripe_dispute.reason,
            disputed_on=datetime.fromtimestamp(stripe_dispute.created, tz=UTC),
            respond_by=(
                datetime.fromtimestamp(stripe_dispute.evidence_details.due_by, tz=UTC)
                if hasattr(stripe_dispute, 'evidence_details')
                and hasattr(stripe_dispute.evidence_details, 'due_by')
                and stripe_dispute.evidence_details.due_by
                else None
            ),
        )

    @staticmethod
    def handle_new_dispute_from_webhook(
        *,
        stripe_dispute: stripe.api_resources.dispute.Dispute,
        event_type: StripeEventType,
    ) -> DisputeResult:
        payment_intent_result = PaymentIntentService.get(stripe_id=stripe_dispute.payment_intent)

        if not payment_intent_result.is_success:
            return DisputeResult(
                is_success=False,
                error=ErrorObject(
                    type=ErrorType.SERVICE_ERROR,
                    code=ServiceErrorCode.PAYMENT_INTENT_DOES_NOT_EXIST.value,
                    message=ServiceErrorCode.PAYMENT_INTENT_DOES_NOT_EXIST.label,
                ),
            )
        dispute, _ = Dispute.objects.get_or_create(
            stripe_id=stripe_dispute.id,
            defaults={
                'payment_intent_id': payment_intent_result.payment_intent.id,
                'subscriber': payment_intent_result.payment_intent.subscriber,
                **DisputeService.retrieve_data(stripe_dispute),
            },
        )
        result = DisputeResult(is_success=True, dispute=dispute.to_dataclass())

        if event := get_registered_event(
            event_type=event_type,
            subscriber=payment_intent_result.payment_intent.subscriber,
        ):
            event.send('stripe_app', result=result.to_dict(), event_type=event_type)

        return result

    @staticmethod
    def handle_dispute_updated_from_webhook(
        *,
        stripe_dispute: stripe.api_resources.dispute.Dispute,
        event_type: StripeEventType,
    ) -> DisputeResult:
        if not (dispute := Dispute.objects.filter(stripe_id=stripe_dispute.stripe_id)):
            return DisputeResult(
                is_success=False,
                error=ErrorObject(
                    type=ErrorType.SERVICE_ERROR,
                    code=ServiceErrorCode.DISPUTE_DOES_NOT_EXIST.value,
                    message=ServiceErrorCode.DISPUTE_DOES_NOT_EXIST.label,
                ),
            )
        dispute.update(**DisputeService.retrieve_data(stripe_dispute))
        result = DisputeResult(is_success=True, dispute=dispute.first().to_dataclass())

        if event := get_registered_event(
            event_type=event_type,
            subscriber=dispute.first().subscriber,
        ):
            event.send('stripe_app', result=result.to_dict(), event_type=event_type)

        return result
