# Generated by Django 2.0.13 on 2020-04-06 13:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('booking', '0081_merge_20200114_1625'),
        ('business', '0270_service_is_online_service'),
    ]

    operations = [
        migrations.CreateModel(
            name='ZoomBusinessCredentials',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('api_key', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('api_secret', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('user_id', models.CharField(blank=True, max_length=100)),
                (
                    'business',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='zoom_credentials',
                        to='business.Business',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ZoomMeeting',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('zoom_meeting_id', models.CharField(max_length=50)),
                (
                    'start_url',
                    models.TextField(
                        help_text='URL to start the meeting. This URL should only be used by the host of the meeting and should not be shared with anyone other than the host.'
                    ),
                ),
                (
                    'join_url',
                    models.TextField(
                        help_text='URL for participants to join the meeting. This URL should only be shared with users that you would like to invite for the meeting.'
                    ),
                ),
                (
                    'booking',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='zoom_meeting',
                        to='booking.Booking',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
    ]
