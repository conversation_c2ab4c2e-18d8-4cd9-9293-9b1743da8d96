# Generated by Django 4.2.18 on 2025-04-03 06:56

from django.db import migrations, models
import lib.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='PeakHourModel',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('business_id', models.IntegerField(db_index=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('service_variant_id', models.IntegerField()),
                ('hour_from', models.TimeField()),
                ('hour_till', models.TimeField()),
                ('elevation_rate', models.DecimalField(decimal_places=0, max_digits=3)),
                (
                    'day_of_week',
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (0, 'SUNDAY'),
                            (1, 'MONDAY'),
                            (2, 'TUESDAY'),
                            (3, 'WEDNESDAY'),
                            (4, 'THURSDAY'),
                            (5, 'FRIDAY'),
                            (6, 'SATURDAY'),
                        ],
                        null=True,
                        db_index=True,
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
