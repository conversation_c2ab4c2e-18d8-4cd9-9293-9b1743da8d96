# Generated by Django 3.2.7 on 2022-01-11 15:02

from django.db import migrations, models
import django.db.models.deletion
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0221_alter_paymentrow_pnref'),
    ]

    operations = [
        migrations.CreateModel(
            name='BsxSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('receipt_printing_enabled', models.BooleanField(default=False)),
                ('open_drawer_after_sale', models.BooleanField(default=False)),
                ('print_operator_name', models.BooleanField(default=False)),
                ('visible_for_biz', models.BooleanField(default=False)),
                (
                    'pos',
                    models.OneToOneField(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='bsx_settings',
                        to='pos.pos',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
