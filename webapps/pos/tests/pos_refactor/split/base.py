# pylint: disable=duplicate-code
from model_bakery import baker

from lib.payment_gateway.enums import PaymentStatus
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketPaymentAnalyticsTrigger,
    BasketPaymentStatus,
    BasketPaymentType,
)
from lib.point_of_sale.enums import PaymentMethodType as PointOfSalePaymentMethodType
from webapps.payment_gateway.models import BalanceTransaction
from webapps.point_of_sale.models import (
    Basket,
    BasketPayment,
    BasketPaymentAnalytics,
)
from webapps.pos.enums import (
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import PaymentType
from webapps.pos.tests.pos_refactor import TestTransactionSerializerBase


class TestTransactionSerializerSplitBase(TestTransactionSerializerBase):
    def setUp(self):
        super().setUp()
        self.pba = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.check = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.CHECK)
        self.split = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.SPLIT)
        self.bgc = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.BOOKSY_GIFT_CARD)

    def make_payment(self, transaction, payment_method, payment_row):
        self.provider.make_payment(
            transaction=transaction,
            payment_method=payment_method,
            payment_row=payment_row,
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            **self.device_data_dict,
        )

    def _check_split_cash_check(self, txn):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 0)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            2,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_split_cash_pba_cfp(self, txn):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.CALL_FOR_PAYMENT)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 0)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_split_cash_pba_success(self, txn):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_split_bgc_pba_success(self, txn, payment_type):
        payment_method_type = {
            PaymentTypeEnum.PAY_BY_APP: PointOfSalePaymentMethodType.CARD,
            PaymentTypeEnum.BLIK: PointOfSalePaymentMethodType.BLIK,
        }[payment_type]
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.BOOKSY_GIFT_CARD,
            provider_code=PaymentProviderCode.BOOKSY_GIFT_CARDS,
            # because we trigger transfer request after checkout is successful
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method_type,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

    def _check_split_bgc_pba_cancel(self, txn, payment_type):
        payment_method_type = {
            PaymentTypeEnum.PAY_BY_APP: PointOfSalePaymentMethodType.CARD,
            PaymentTypeEnum.BLIK: PointOfSalePaymentMethodType.BLIK,
        }[payment_type]
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.GIFT_CARD_DEPOSIT)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.BOOKSY_GIFT_CARD,
            provider_code=PaymentProviderCode.BOOKSY_GIFT_CARDS,
            # because we trigger transfer request after checkout is successful
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method_type,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        # Balance Transaction
        self.assertEqual(BalanceTransaction.objects.count(), 1)

    def _check_split_cash_pba_send_for_refund(self, txn, success_row):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.SENT_FOR_REFUND)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self.assertEqual(BalanceTransaction.objects.count(), 2)  # payment + refund

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_split_cash_pba_refund(self, txn, success_row):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.REFUNDED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self._check_refund_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_split_cash_pba_refund_fail(self, txn, success_row):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.FAILED,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self._check_refund_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_split_cash_pba_chargeback(self, txn, success_row):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self._check_chargeback_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_split_cash_pba_chargeback_reversed(self, txn, success_row, chargeback_row):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.CHARGEBACK_REVERSED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=chargeback_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK_REVERSED,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 4)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)

    def _check_split_cash_pba_second_chargeback(
        self, txn, success_row, chargeback_row, chargeback_reversed_row
    ):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 5)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.SECOND_CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CHECK,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=chargeback_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=chargeback_reversed_row,
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK_REVERSED,
            balance_transaction_existing=True,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=PointOfSalePaymentMethodType.CARD,
            provider_code=self.provider_code,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.SECOND_CHARGEBACK,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 5)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 3)
