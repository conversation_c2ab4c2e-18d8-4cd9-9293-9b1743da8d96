from typing import Optional


from lib.business.entities import ServiceVariantPaymentEntity
from lib.tools import clean_string
from webapps.business.enums import NoShowProtectionType
from webapps.business.models import Business, ServiceVariantPayment


def get_business_statement_name(business_id: int) -> Optional[str]:
    business = Business.objects.filter(id=business_id).first()
    if not business:
        return None

    return clean_string(business.name)


def get_business_marketplace_desktop_url(business_id: int, staffer_id: Optional[int] = None) -> str:
    business = Business.objects.get(pk=business_id)
    return business.get_marketplace_url_for_desktop_deeplink(staffer_id=staffer_id)


def get_service_variant_payment(
    service_variant_id: int,
    payment_type: NoShowProtectionType,
) -> ServiceVariantPaymentEntity | None:
    if service_variant_payment := ServiceVariantPayment.objects.filter(
        service_variant_id=service_variant_id,
        payment_type=payment_type,
    ).first():
        return service_variant_payment.entity
    return None
