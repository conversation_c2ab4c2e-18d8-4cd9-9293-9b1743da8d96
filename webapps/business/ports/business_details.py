from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from webapps.billing.entities.transaction import BusinessBillingEntity


@dataclass(frozen=True)
class BusinessDetailsEntity:
    id: int
    name: str
    address: str
    timezone: datetime.tzinfo


class BusinessDetailsPort:
    @staticmethod
    def get_business_details(business_id: int) -> Optional[BusinessDetailsEntity]:
        """
        Fetch business details by ID

        :param business_id: ID of the business
        :return: BusinessDetailsEntity or None if business not found
        """
        from webapps.business.models import Business

        business = Business.objects.filter(id=business_id).first()
        if not business:
            return None

        return BusinessDetailsEntity(
            id=business.id,
            name=business.name,
            address=business.get_location_name(with_city=True),
            timezone=business.get_timezone(),
        )

    @staticmethod
    def get_business_billing_data(business_id: int) -> Optional[BusinessBillingEntity]:
        """
        Fetch business billing data by ID for fraud prevention services

        :param business_id: ID of the business
        :return: BusinessBillingEntity or None if business not found
        """
        from webapps.business.models import Business

        business = Business.objects.select_related('owner').filter(id=business_id).first()
        if not business:
            return None

        return BusinessBillingEntity(
            id=business.id,
            created_at=business.created,
            updated_at=business.updated,
            longitude=business.longitude,
            latitude=business.latitude,
            owner_first_name=business.owner.first_name,
            owner_last_name=business.owner.last_name,
            phone_number=business.phone,
            email=business.public_email,
            name=business.name,
            country=business.country_code,
            city=business.city,
            postal_code=business.zipcode,
            address=business.address,
            state=business.state,
        )
