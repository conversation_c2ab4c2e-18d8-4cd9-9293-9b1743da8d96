# UTTv1

## Related API endpoints:
* business app API:
  * `/business_api/categories/?` - 
  [CategoriesHandler.get](service/business/category.py) - list of all categories
  * `/business_api/me/businesses/(?P<business_id>\d+)/categories/?` - 
  [BusinessCategoriesHandler.get](service/business/category.py) - list of all categories **visible** for business

## Features
1. [UTT update](#utt-update)
2. [Treatment assignment](#treatment-assignment)
3. [Service suggestion](#service-suggestion)

### UTT update
Structure of UTT (set of categories and treatments) can be updated 
with properly formatted json or csv file uploaded to 
[Booksy bucket](/lib/gcs/README.md#buckets-and-access) with `UTT/v1/<country_code>/` prefix.
Categories and treatments are updated by separated files: `categories__<country_code>.csv` / 
`treatments__<country_code>.csv`.

To trigger structure updating use
[`BusinessCategoriesModifier`](webapps/business/business_categories/modifier.py) class: 
``` python
from webapps.business.business_categories.modifier import BusinessCategoriesModifier

modifier = BusinessCategoriesModifier()
modifier.run(country_code='<country_code>', file_format='<file_format>')
```

### Treatment assignment
Treatment is automatically assigned to service after save on instance creation if service is named.
Assigned is realized with [`TreatmentAssignmentUTT1`](webapps/business/business_categories/treatment_assignment.py) 
class' `assign_treatments_for_services` method. 

After each assignment business' treatments and categories sets are updated 
with `update_businesses_treatments` method. This method is also triggered on `Service` 
model `assign_treatment` method.

Updating business categories set currently **do not remove any assigned category**, 
because of current subcategory implementation and possibility of selecting categories by provider.
Needs to be removed in v2.

#### Related endpoints:
* business app API:
  * `/business_api/me/businesses/(?P<business_id>\d+)/services/?`
  [ServicesHandler.post](service/business/services.py) - service creation triggers 
  treatment assigning
  * `/business_api/me/businesses/(?P<business_id>\d+)/services/(?P<service_id>\d+)?`
  [ServicesHandler.put](service/business/services.py) - service name update 
  triggers treatment assigning

### Service suggestion
Service suggestion is a predefined service suggested to a business owner during the process of creating 
a new business or adding a new service to the existing business. A suggestion is presented with recommended 
values of price and duration of service. If a suggestion doesn't have its dedicated values, 
then the default price (33 units of local currency) and duration (10 minutes) are shown. 
The list of suggestions depends on the region and it can be updated in a similar way to updating a UTT structure. 
The properly formatted CSV file (`service_suggestions__<country_code>.csv`) has to be uploaded to Booksy bucket 
with `UTT/v1/<country_code>/` prefix.

Triggering is also similar, you have to use
[`ServiceSuggestionModifier`](webapps/business/business_categories/service_suggestion.py) class: 
``` python
from webapps.business.business_categories.service_suggestion import ServiceSuggestion

modifier = ServiceSuggestionModifier()
modifier.run()
```

The class [`ServiceSuggestionUtil`](webapps/business/business_categories/service_suggestion.py) contains two methods, 
which allow suggesting services. Method `suggest_services` returns services based on its name, when `suggest_services_for_categories` 
returns the most popular services for given categories. Suggested treatments are limited to categories of business in both cases, 
so in the other words, it's not possible to suggest treatment from a category, that business doesn't belong.  
 

#### Related endpoints:
* business app API:
  * `/business_api/me/businesses/(\d+)/service_hints/?`
  [ServiceHintsHandler.get](service/business/service_suggestion.py) - if the field 'text' is present in a request data,
   returns auto-completion hints for suggested services based. Otherwise, returns the most popular services of categories.
  * `/business_api/me/businesses/(\d+)/service_hints/?`
  [ServiceHintsHandler.post](service/business/service_suggestion.py) - adds services to business for given treatment ids 
  * `/business_api/me/businesses/(\d+)/service_suggestion/?)?`
  [ServiceSuggestionHandler.post](service/business/service_suggestion.py) - creates initial services based on country and
  business categories - this handler it's used only during the onboarding of the new business
