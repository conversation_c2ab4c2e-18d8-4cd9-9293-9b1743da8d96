# Generated by Django 1.10.7 on 2017-08-10 09:23
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0025_auto_20170721_1052'),
        ('business', '0086_merge_20170721_1049'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='businesscustomerinfo',
            name='recurring',
        ),
        migrations.AddField(
            model_name='businesscustomerinfo',
            name='client_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('UN', 'Unknown'),
                    ('BD', 'Added by business directly'),
                    ('BI', 'Added by business by import'),
                    ('BV', 'Added by business via invite'),
                    ('BS', 'Added by business via subdomain/deeplink'),
                    ('CR', 'Recurring customer'),
                    ('CN', 'New customer'),
                    ('GP', 'Google Partner'),
                    ('LO', 'Legacy'),
                ],
                default='UN',
                max_length=2,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='businesscustomerinfo',
            name='first_booking',
            field=models.OneToOneField(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='business_customer_info',
                to='booking.Booking',
            ),
        ),
        migrations.AddField(
            model_name='businesscustomerinfo',
            name='source',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='business_customers_info',
                to='booking.BookingSources',
            ),
        ),
    ]
