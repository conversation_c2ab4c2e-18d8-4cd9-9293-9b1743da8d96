# Generated by Django 4.1.7 on 2023-04-28 18:01

from django.db import migrations


MIGRATION = """
DROP TRIGGER IF EXISTS servicevariant_protect_price_not_null ON public.business_servicevariant;
DROP FUNCTION IF EXISTS servicevariant_protect_price_not_null();
"""
ROLLBACK = """
CREATE FUNCTION servicevariant_protect_price_not_null() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    BEGIN
        IF NEW.price IS NULL and NEW."type" IN ('S', 'X') THEN
            RAISE 'price cannot be NULL for type "%", service_variant_id = %', NEW."type", NEW.service_variant_id;
        ELSE
            RETURN NEW;
        END IF;
    END;
    $$;

CREATE TRIGGER servicevariant_protect_price_not_null BEFORE INSERT OR UPDATE ON public.business_servicevariant FOR EACH ROW EXECUTE PROCEDURE public.servicevariant_protect_price_not_null();
"""


class Migration(migrations.Migration):
    dependencies = [
        ('business', '0409_bciversumagreement'),
    ]

    operations = [
        migrations.RunSQL(MIGRATION, ROLLBACK),
    ]
