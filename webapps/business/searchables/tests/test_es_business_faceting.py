# -*- coding: utf-8 -*-

from collections import OrderedDict
from enum import IntEnum

import pytest
from mock import patch
from slugify import slugify

from lib.elasticsearch.consts import ESDocType
from service.search.serializers import CategoryFacetingSerializer, RegionFacetingSerializer
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.searchables.business import (
    BusinessCategoryFacetingSearchable,
    BusinessRegionFacetingSearchable,
    BusinessTreatmentFacetingSearchable,
)
from webapps.business.searchables.serializers import BusinessSearchHitSerializer
from webapps.business.searchables.tests.utils import (
    BCE,
    CATEGORY_TREATMENT_MAPPING,
    TE,
    TREATMENT_ID_CATEGORY_ID_MAPPING,
    generate_business_name,
    mock_categories_mapping,
)
from webapps.structure.constants import CITY_REGION_TYPE, COUNTRY, ZIP_REGION_TYPE
from webapps.structure.elasticsearch import RegionDocument


class RE(IntEnum):
    POLAND = 1
    WROCLAW = 2
    POZNAN = 3
    # ZIPCODE Wrocław
    Z53014 = 4
    Z52333 = 5
    Z52135 = 6
    # ZIPCODE Poznań
    Z60158 = 7
    Z60514 = 8
    Z61151 = 9


LOCATION_MAPPING = {
    RE.POLAND: (52.237049, 21.017532),
    RE.WROCLAW: (51.107883, 17.038538),
    RE.POZNAN: (52.4006332791, 16.9197833506),
    RE.Z53014: (51.07061, 17.00923),
    RE.Z52333: (51.058937, 16.9773),
    RE.Z52135: (51.059193, 17.0893),
    RE.Z60158: (52.389858, 16.864363),
    RE.Z60514: (52.411098, 16.897683),
    RE.Z61151: (52.389442, 16.942348),
}


def _get_lat_lon(region):
    return LOCATION_MAPPING[region]


def get_parents_w():
    return [RE.POLAND.value, RE.WROCLAW.value]


def get_parents_p():
    return [RE.POLAND.value, RE.POZNAN.value]


RELATIONSHIP_MAPPING = {
    RE.WROCLAW: [RE.Z53014.value, RE.Z52333.value, RE.Z52135.value],
    RE.Z53014: get_parents_w(),
    RE.Z52333: get_parents_w(),
    RE.Z52135: get_parents_w(),
    RE.POZNAN: [RE.Z60158.value, RE.Z60514.value, RE.Z61151.value],
    RE.Z60158: get_parents_p(),
    RE.Z60514: get_parents_p(),
    RE.Z61151: get_parents_p(),
}


def gen_number_business(number_business):
    assert len(number_business) == 6
    data = OrderedDict()
    # order is important to check business ids then in tests
    data[RE.Z53014] = number_business[0]
    data[RE.Z52333] = number_business[1]
    data[RE.Z52135] = number_business[2]
    data[RE.WROCLAW] = sum(number_business[:3])
    data[RE.Z60158] = number_business[3]
    data[RE.Z60514] = number_business[4]
    data[RE.Z61151] = number_business[5]
    data[RE.POZNAN] = sum(number_business[3:6])
    # For whole category
    data[None] = sum(number_business)
    return data


_NUMBER_BUSINESSES_IN_CATEGORY_PER_REGION_MAPPING = {
    # Z53014, Z52333, Z52135, Z60158, Z60514, Z61151
    BCE.HAIR_SALONS: gen_number_business([1, 2, 3, 2, 1, 0]),
    BCE.BARBERS: gen_number_business([0, 1, 3, 0, 5, 0]),
    BCE.MASSAGE: gen_number_business([0, 3, 0, 2, 1, 1]),
    BCE.SPA: gen_number_business([0, 1, 0, 0, 0, 0]),
    BCE.SKIN_CARE: gen_number_business([2, 0, 0, 0, 2, 0]),
}


def get_expected_number_businesses(category, region):
    return _NUMBER_BUSINESSES_IN_CATEGORY_PER_REGION_MAPPING[category][region]


@pytest.fixture(scope='module', autouse=True)
def create_data(clean_index_module_fixture):
    """Prepare data for test
    :return:
    """
    create_regions(clean_index_module_fixture)
    create_business(clean_index_module_fixture)


def create_regions(clean_index_module_fixture):
    index = clean_index_module_fixture(ESDocType.REGION)

    for region, name, parents, children, _type in (
        (
            RE.POLAND,
            'Poland',
            [],
            [RE.WROCLAW.value, RE.POZNAN.value],
            COUNTRY,
        ),
        (
            RE.WROCLAW,
            'Wrocław',
            [RE.POLAND.value],
            None,
            CITY_REGION_TYPE,
        ),
        (
            RE.POZNAN,
            'Poznań',
            [RE.POLAND.value],
            None,
            CITY_REGION_TYPE,
        ),
        (
            RE.Z53014,
            '53-014',
            None,
            [],
            ZIP_REGION_TYPE,
        ),
        (
            RE.Z52333,
            '52-333',
            None,
            [],
            ZIP_REGION_TYPE,
        ),
        (
            RE.Z52135,
            '52-135',
            None,
            [],
            ZIP_REGION_TYPE,
        ),
        (
            RE.Z60158,
            '60-158',
            None,
            [],
            ZIP_REGION_TYPE,
        ),
        (
            RE.Z60514,
            '60-514',
            None,
            [],
            ZIP_REGION_TYPE,
        ),
        (
            RE.Z61151,
            '61-151',
            None,
            [],
            ZIP_REGION_TYPE,
        ),
    ):
        latitude, longitude = _get_lat_lon(region)
        if parents is None:
            parents = RELATIONSHIP_MAPPING.get(region, [])
        elif children is None:
            children = RELATIONSHIP_MAPPING.get(region, [])
        RegionDocument(
            _id=f'region:{region.value}',
            id=region.value,
            name=name,
            latitude=latitude,
            longitude=longitude,
            type=_type,
            canonical_parent=parents,
            canonical_children=children,
        ).save()

    index.refresh()


def create_business(clean_index_module_fixture):
    """Create businesses for test

    Wrocław: Z53014, Z52333, Z52135
    Poznan: Z60158, Z60514, Z61151
    See NUMBER_BUSINESSES_IN_CATEGORY_PER_REGION_MAPPING

    :return: None
    """
    index = clean_index_module_fixture(ESDocType.BUSINESS)
    init_data = (
        BCE.HAIR_SALONS,
        BCE.BARBERS,
        BCE.MASSAGE,
        BCE.SPA,
        BCE.SKIN_CARE,
    )
    treatment_names = TE.get_id_name()
    # reindex
    business_index_counter = 1
    for category in init_data:
        businesses_per_region = _NUMBER_BUSINESSES_IN_CATEGORY_PER_REGION_MAPPING[category]
        treatments = [
            dict(id=t.value, name=treatment_names[t.value])
            for t in CATEGORY_TREATMENT_MAPPING[category]
        ]
        for region, num_business in businesses_per_region.items():

            if (
                # number of businesses for whole category
                region is None
                or
                # total number of businesses in each city
                region == RE.WROCLAW
                or region == RE.POZNAN
            ):
                continue
            regions = RELATIONSHIP_MAPPING.get(region)
            local_region = regions[:]
            local_region.append(region.value)
            latitude, longitude = _get_lat_lon(region)

            for _ in range(num_business):
                BusinessDocument(
                    id=business_index_counter,
                    _id=f'business:{business_index_counter}',
                    name=generate_business_name(),
                    visible=True,
                    popularity=0,
                    active=True,
                    join='business',
                    business_categories=[dict(id=category.value)],
                    treatments=treatments,
                    business_primary_category=dict(id=category.value),
                    business_location=dict(
                        coordinate=dict(
                            lat=latitude,
                            lon=longitude,
                        ),
                    ),
                    is_b_listing=False,
                    regions=[dict(id=r) for r in local_region],
                ).save()
                business_index_counter += 1

    index.refresh()


@pytest.mark.parametrize(
    'data, expected',
    [
        (
            dict(
                category=[int(category) for category in BCE],
            ),
            5,
        ),
        (dict(category=[]), 5),
        (
            dict(
                category=[
                    BCE.HAIR_SALONS.value,
                    BCE.BARBERS.value,
                ]
            ),
            2,
        ),
        (
            dict(
                category=[
                    BCE.NAIL_SALONS.value,
                ]
            ),
            0,
        ),
    ],
)
def test_business_facet_categories(data, expected):
    searchable = BusinessCategoryFacetingSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    resp = searchable.execute(data)
    aggs = getattr(resp.aggregations, 'categories', None)
    assert aggs is not None, f'Data {data}'
    aggregated_categories = aggs.categories

    actual_number = len(aggregated_categories)
    assert actual_number == expected, f'Expected categories {expected}; Got {actual_number}'
    for bucket in aggregated_categories:
        category = BCE(bucket.key)
        expected_businesses = get_expected_number_businesses(category, None)
        assert bucket.doc_count == expected_businesses


@pytest.mark.parametrize(
    'data, expected_number, expected_aggs',
    [
        (
            dict(location_id=RE.WROCLAW.value),
            16,
            {
                RE.Z53014: 3,
                RE.Z52333: 7,
                RE.Z52135: 6,
            },
        ),
        (
            dict(location_id=RE.POZNAN.value),
            14,
            {
                RE.Z60158: 4,
                RE.Z60514: 9,
                RE.Z61151: 1,
            },
        ),
        (
            dict(location_id=RE.POLAND.value),
            30,
            {
                RE.WROCLAW: 16,
                RE.POZNAN: 14,
            },
        ),
        # zipcode do not children
        (
            dict(location_id=RE.Z61151.value),
            # all other zipcodes might overlap by radius 3km with others
            # this zipcod is away form everything
            1,
            {},
        ),
    ],
)
def test_business_facet_regions(data, expected_number, expected_aggs):
    serializer = RegionFacetingSerializer(data=data)
    assert serializer.is_valid(), f'Errors {serializer.errors}'
    # search
    data = serializer.validated_data
    searchable = BusinessRegionFacetingSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    resp = searchable.params(_source=False, size=30).execute(data)
    assert len(resp.hits) == expected_number
    if not expected_aggs:
        return
    aggs = getattr(resp.aggregations, 'regions', None)
    assert aggs is not None, f'Data {data}'
    for bucket in aggs.regions:
        region = RE(bucket.key)
        assert bucket.doc_count == expected_aggs[region]


def mocked_treatments():
    return {
        name: dict(
            id=t_id,
            name=name,
            slug=slugify(name),
            parent=TREATMENT_ID_CATEGORY_ID_MAPPING[t_id],
        )
        for t_id, name in TE.get_id_name().items()
    }


@patch('service.search.serializers.get_es_category_mapping')
@patch(
    'webapps.business.business_categories.cache.TreatmentCache._get_all',
    side_efect=mocked_treatments,
)
@pytest.mark.parametrize(
    'data, expected_hits',
    [
        (
            dict(location_id=RE.WROCLAW.value, category=[BCE.HAIR_SALONS.value]),
            # id of businesses
            {1, 2, 3, 4, 5, 6},
        ),
        (
            dict(location_id=RE.POZNAN.value, category=[BCE.BARBERS.value]),
            # id of businesses
            {14, 15, 16, 17, 18},
        ),
        (
            dict(location_id=RE.POLAND.value, category=[BCE.SPA.value]),
            # id of businesses
            {26},
        ),
        (
            dict(location_id=RE.POLAND.value, category=[BCE.SKIN_CARE.value]),
            # id of businesses
            {27, 28, 29, 30},
        ),
    ],
)
def test_business_filter_category_facet_regions(__, mocked_mapping, data, expected_hits):
    mock_categories_mapping(BCE, mocked_mapping)
    serializer = RegionFacetingSerializer(data=data)
    assert serializer.is_valid(), f'Errors {serializer.errors}'
    # search
    data = serializer.validated_data
    searchable = BusinessRegionFacetingSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    resp = searchable.execute(data)
    assert {
        h.id for h in resp
    } == expected_hits, f'Expected hits {expected_hits}; Got {set(h.id for h in resp)}'

    aggs = getattr(resp.aggregations, 'regions', None)
    category = BCE(data['category'][0])
    assert aggs is not None, f'Data {data}'
    for bucket in aggs.regions:
        region = RE(bucket.key)
        expected_businesses = get_expected_number_businesses(category, region)
        assert bucket.doc_count == expected_businesses


@patch('service.search.serializers.get_es_category_mapping')
@patch(
    'webapps.business.business_categories.cache.TreatmentCache._get_all',
    side_efect=mocked_treatments,
)
@pytest.mark.parametrize(
    'data, expected',
    [
        # TODO not test with location_geo it will
        #  fetch location from here_maps
        (
            dict(location_id=RE.POZNAN.value, treatment=[TE.BEARD_SHAVE.value]),
            {
                TE.BEARD_SHAVE.value: 5,
                TE.BEARD_TRIM.value: 5,
                TE.SHAVE.value: 5,
            },
        ),
        (
            dict(location_id=RE.Z60514.value, treatment=[TE.WAXING.value]),
            {
                TE.WAXING.value: 2,
                TE.BOTOX.value: 2,
                TE.COSMELAN.value: 2,
            },
        ),
        (
            dict(location_id=RE.Z60158.value, treatment=[TE.BOTOX.value]),
            {
                TE.WAXING.value: 0,
                TE.BOTOX.value: 0,
                TE.COSMELAN.value: 0,
            },
        ),
        # empty test
        (dict(location_id=RE.WROCLAW.value, treatment=[TE.MANICURE.value]), {}),
        (
            dict(location_id=RE.WROCLAW.value, category=[BCE.SPA.value]),
            {
                TE.SAUNA.value: 1,
                TE.BODY_SCRUB.value: 1,
                TE.MAKE_UP.value: 1,
            },
        ),
        (dict(location_id=RE.POZNAN.value, category=[BCE.SPA.value]), {}),
    ],
)
def test_business_treatment_facet(__, mocked_mapping, data, expected):
    # init add category of treatment
    treatment = data.get('treatment')
    if treatment is not None:
        data['category'] = [TREATMENT_ID_CATEGORY_ID_MAPPING[treatment[0]]]
    mock_categories_mapping(BCE, mocked_mapping)
    # search
    serializer = CategoryFacetingSerializer(data=data)
    assert serializer.is_valid(), f'Errors {serializer.errors}'
    # search
    data = serializer.validated_data
    searchable = BusinessTreatmentFacetingSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    resp = searchable.execute(data)
    aggs = getattr(resp.aggregations, 'treatments', None)
    assert aggs is not None, f'Data {data}'
    for bucket in aggs.treatments:
        assert (
            bucket.key in expected
        ), f"This treatment {bucket.key} not supposed to appear: expected:  {expected.keys()}"

        expected_businesses = expected[bucket.key]
        assert bucket.doc_count == expected_businesses
