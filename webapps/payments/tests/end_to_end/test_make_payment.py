from unittest.mock import patch

import mock
from django.db.models import Q
from django.test.utils import override_settings
from django.urls import reverse
from model_bakery import baker

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.payment_gateway.enums import PaymentMethodType
from lib.payments.enums import PaymentProviderCode
from lib.payment_providers.enums import ExternalPaymentMethodType
from webapps.booking.models import BookingSources
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import (
    Customer,
    Payment,
    StripeCustomer,
    StripeTokenizedPaymentMethod,
    TokenizedPaymentMethod,
    StripeAccountHolder,
)
from webapps.point_of_sale.models import BasketPayment
from webapps.pos.enums import PaymentProviderEnum, PaymentTypeEnum
from webapps.pos.models import PaymentMethod, PaymentType, Transaction
from webapps.pos.provider import get_payment_provider
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeMixin
from webapps.pos.tests.pos_refactor.pba.base import TestTransactionSerializerPBABase
from webapps.stripe_integration.models import StripeAccount


class MakePaymentFullWorkflowTest(
    CustomerAPITestCase,
    StripeMixin,
    TestTransactionSerializerPBABase,
):
    @classmethod
    def setUpTestData(cls):
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def setUp(self):
        super().setUp()
        self.analytics_filters = (Q(device_data__phone_number='*********'),)

        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        self.pba = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.payment_method = baker.make(
            PaymentMethod, provider=PaymentProviderEnum.STRIPE_PROVIDER
        )
        self.provider = get_payment_provider(
            codename=self.payment_method.provider,
            txn=Transaction(pos=self.pos),
        )

        business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        baker.make(
            StripeAccountHolder,
            account_holder_id=business_wallet.account_holder_id,
            external_id='123',
        )
        baker.make(
            StripeAccount,
            pos=self.pos,
            kyc_verified_at_least_once=True,
            external_id='123',
        )

        customer = Customer.objects.last()
        baker.make(
            StripeCustomer,
            customer=customer,
        )
        self.tokenized_payment_method = baker.make(
            TokenizedPaymentMethod,
            customer=customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details={
                'brand': 'visa',
                'last_digits': '1234',
                'expiry_month': 1,
                'expiry_year': 2032,
                'cardholder_name': 'cardholder name',
            },
        )

        baker.make(
            StripeTokenizedPaymentMethod,
            external_id='1233333',
            tokenized_payment_method=self.tokenized_payment_method,
        )
        self.provider_code = PaymentProviderCode.STRIPE
        self.random_id = self.generate_id()

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)
        self.basket_payment = BasketPayment.objects.filter(
            basket_id=txn.basket_id,
        ).last()

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={
                    'basket_payment_id': str(
                        self.basket_payment.id,
                    )
                },
            ),
            data={
                'tokenized_payment_method_id': str(
                    self.tokenized_payment_method.id,
                ),
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json()['basket_payment']['id'],
            str(self.basket_payment.id),
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success(txn=txn)

    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_call_for_payment_3ds(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_3ds_required_response(confirm_payment_intent_mock, self.random_id)
        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction, but additional verification is needed
        self.basket_payment = BasketPayment.objects.filter(
            basket_id=txn.basket_id,
        ).last()

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={
                    'basket_payment_id': str(
                        self.basket_payment.id,
                    )
                },
            ),
            data={
                'tokenized_payment_method_id': str(
                    self.tokenized_payment_method.id,
                ),
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json()['basket_payment']['id'],
            str(self.basket_payment.id),
        )

        txn.refresh_from_db()
        tds_data = Payment.objects.order_by('-created').first().action_required_details
        self._check_pba_call_for_payment_3ds(txn=txn, tds_data=tds_data)

    @override_settings(POS__GOOGLE_PAY=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.capture_payment_intent')
    def test_pba_success_google_pay(
        self, _capture_payment_intent_mock, confirm_payment_intent_mock, create_payment_intent_mock
    ):
        create_payment_intent_mock.return_value = mock.MagicMock(id=self.random_id)
        self._mock_auth_response(confirm_payment_intent_mock, True, self.random_id)

        txn = self.create_transaction(PaymentTypeEnum.PAY_BY_APP)

        # Pay for transaction
        self.basket_payment = BasketPayment.objects.filter(
            basket_id=txn.basket_id,
        ).last()

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={
                    'basket_payment_id': str(
                        self.basket_payment.id,
                    )
                },
            ),
            data={
                'external_payment_method': {
                    'partner': ExternalPaymentMethodType.GOOGLE_PAY,
                    'token': '123',
                }
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json()['basket_payment']['id'],
            str(self.basket_payment.id),
        )

        self._success_payment_notifications(external_id=self.random_id)

        txn.refresh_from_db()
        self._check_pba_success_google_pay(txn=txn)
