from django.core.management.base import BaseCommand
from webapps.pos.models import POSPlan
from webapps.pos.enums import POSPlanPaymentTypeEnum, PaymentProviderEnum
from webapps.pos.tasks import recalculate_pos_plans_for_payment_type
from webapps.payment_gateway.models import (
    WalletFeeSettings,
)


class Command(BaseCommand):
    help = 'Script to create new POS plan for Stripe Mobile Payment'

    def add_arguments(self, parser):
        parser.add_argument(
            '--provision',
            type=float,
            default=0.0189,
            help='',
            dest='provision',
        )

        parser.add_argument(
            '--txn_fee',
            type=float,
            default=0.49,
            help='',
            dest='txn_fee',
        )

        parser.add_argument(
            '--refund_provision',
            type=float,
            default=0.0189,
            help='',
            dest='refund_provision',
        )

        parser.add_argument(
            '--refund_txn_fee',
            type=float,
            default=0.49,
            help='',
            dest='refund_txn_fee',
        )

        parser.add_argument(
            '--chargeback_provision',
            type=float,
            default=0,
            help='',
            dest='chargeback_provision',
        )

        parser.add_argument(
            '--chargeback_txn_fee',
            type=float,
            default=100.0,
            help='',
            dest='chargeback_txn_fee',
        )

        parser.add_argument(
            '--fast_payout_provision_percentage',
            type=float,
            default=0,
            help='',
            dest='fast_payout_provision_percentage',
        )

        parser.add_argument(
            '--fast_payout_provision_fee',
            type=int,
            default=0.0,
            help='',
            dest='fast_payout_provision_fee',
        )

        parser.add_argument(
            '--regular_payout_provision_percentage',
            type=float,
            default=0.0,
            help='',
            dest='regular_payout_provision_percentage',
        )

        parser.add_argument(
            '--regular_payout_provision_fee',
            type=int,
            default=0,
            help='',
            dest='regular_payout_provision_fee',
        )

    def handle(self, *args, **opts):  # pylint: disable=unused-argument
        POSPlan.objects.update_or_create(
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
            defaults={
                'provision': opts.get('provision'),
                'txn_fee': opts.get('txn_fee'),
                'refund_provision': opts.get('refund_provision'),
                'refund_txn_fee': opts.get('refund_txn_fee'),
                'chargeback_provision': opts.get('chargeback_provision'),
                'chargeback_txn_fee': opts.get('chargeback_txn_fee'),
            },
        )
        recalculate_pos_plans_for_payment_type(
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT
        )

        WalletFeeSettings.objects.update_or_create(
            payment_provider_code=PaymentProviderEnum.STRIPE_PROVIDER,
            fast_payout_provision_percentage=opts.get('fast_payout_provision_percentage'),
            fast_payout_provision_fee=opts.get('fast_payout_provision_fee'),
            regular_payout_provision_percentage=opts.get('regular_payout_provision_percentage'),
            regular_payout_provision_fee=opts.get('regular_payout_provision_fee'),
            default=True,
        )
