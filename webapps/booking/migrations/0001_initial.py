from django.db import models, migrations
import dirtyfields.dirtyfields
import lib.fields.phone_number
import django.contrib.postgres.fields.ranges


BASE_SOURCES = [  # ACHTUNG: non-live only, live ones are secret ;)
    ('iPhone', 'iphone-11aa765b-c66f-492a-b77a-dac47b6016ba', 'C'),
    ('iPhone', 'iphone-biz-26d69fc8-57e5-4cc3-a46a-9e345556d61a', 'B'),
    ('Widget', 'widget-efab2853-7166-492f-b78f-925d33698cd4', 'C'),
    ('Widget', 'widget-biz-466c5755-d5b2-43ce-8d33-783246f0d025', 'B'),
    ('Web', 'web-af3c6e80-8420-11e3-baa7-0800200c9a66', 'C'),
    ('Web', 'web-biz-66e84d5a-d8cf-498f-9123-cc8aec9cdf4d', 'B'),
    ('Internal', 'internal-4800ac16-39cf-439f-8cbf-fe338f06059b', 'I'),
    ('Widget test', 'widget-efab2853-7166-492f-b78f-925d33698cd4-test', 'C'),
    ('Android', 'android-11aa765b-c66f-492a-b77a-dac47b6016ba', 'C'),
    ('Android', 'android-biz-26d69fc8-57e5-4cc3-a46a-9e345556d61a', 'B'),
]


def create_base_sources(apps, schema_editor):
    db_name = schema_editor.connection.alias
    BookingSources = apps.get_model('booking', 'BookingSources')
    BookingSources.objects.using(db_name).bulk_create(
        [
            BookingSources(name=name, api_key=api_key, app_type=app_type)
            for name, api_key, app_type in BASE_SOURCES
        ]
    )


class Migration(migrations.Migration):

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                ('id', models.AutoField(serialize=False, primary_key=True, db_column='booking_id')),
                ('booked_from', models.DateTimeField()),
                ('booked_till', models.DateTimeField()),
                (
                    'type',
                    models.CharField(
                        max_length=1,
                        choices=[
                            (
                                'B',
                                'Created by business  - customer matched either by phone or email',
                            ),
                            ('C', 'Created by customer'),
                            ('T', 'Resource time off - not available - auto booking'),
                            ('R', 'Resource time reservation - resource not available'),
                        ],
                    ),
                ),
                ('customer_note', models.CharField(max_length=1500, null=True, blank=True)),
                (
                    'status',
                    models.CharField(
                        max_length=1,
                        choices=[
                            ('A', 'Confirmed'),
                            ('C', 'Canceled'),
                            ('D', 'Declined'),
                            ('F', 'Finished'),
                            ('M', 'Modified'),
                            ('P', 'Proposed'),
                            ('N', 'No show'),
                            ('R', 'Rejected'),
                            ('V', 'Awaiting email (account) verification'),
                            ('W', 'Waiting for confirmation (by business)'),
                        ],
                    ),
                ),
                ('status_changed', models.DateTimeField(auto_now_add=True, null=True)),
                ('business_note', models.CharField(max_length=1500, null=True, blank=True)),
                ('autoassign', models.BooleanField()),
                ('customer_name', models.CharField(max_length=61, null=True, blank=True)),
                ('service_name', models.CharField(max_length=50, null=True, blank=True)),
                (
                    'customer_phone',
                    lib.fields.phone_number.BooksyPhoneNumberField(
                        max_length=50, null=True, blank=True
                    ),
                ),
                ('customer_email', models.EmailField(max_length=75, null=True, blank=True)),
                ('archived', models.BooleanField(default=False)),
            ],
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
        migrations.CreateModel(
            name='BookingHistory',
            fields=[
                ('id', models.AutoField(serialize=False, primary_key=True, db_column='history_id')),
                ('booked_from', models.DateTimeField()),
                ('booked_till', models.DateTimeField()),
                (
                    'status',
                    models.CharField(
                        max_length=1,
                        choices=[
                            ('A', 'Confirmed'),
                            ('C', 'Canceled'),
                            ('D', 'Declined'),
                            ('F', 'Finished'),
                            ('M', 'Modified'),
                            ('P', 'Proposed'),
                            ('N', 'No show'),
                            ('R', 'Rejected'),
                            ('V', 'Awaiting email (account) verification'),
                            ('W', 'Waiting for confirmation (by business)'),
                        ],
                    ),
                ),
                ('customer_note', models.CharField(max_length=1500, null=True, blank=True)),
                ('business_note', models.CharField(max_length=1500, null=True, blank=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='BookingResource',
            fields=[
                (
                    'id',
                    models.AutoField(
                        verbose_name='ID', serialize=False, auto_created=True, primary_key=True
                    ),
                ),
                (
                    'booking',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='bookingresources',
                        to='booking.Booking',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='BookingSources',
            fields=[
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='booking_source_id'
                    ),
                ),
                ('name', models.CharField(max_length=20)),
                ('api_key', models.TextField(unique=True)),
                (
                    'app_type',
                    models.CharField(
                        max_length=1,
                        choices=[('B', 'Business'), ('C', 'Customer'), ('I', 'Internal')],
                    ),
                ),
                ('theme', models.CharField(max_length=50)),
            ],
        ),
        migrations.RunPython(create_base_sources, migrations.RunPython.noop),
        migrations.CreateModel(
            name='MultiBooking',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='multibooking_id'
                    ),
                ),
            ],
            options={
                'abstract': False,
                'get_latest_by': 'updated',
            },
        ),
        migrations.CreateModel(
            name='RejectionReason',
            fields=[
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='booking_rejection_reason_id'
                    ),
                ),
                ('codename', models.CharField(unique=True, max_length=100)),
                ('name', models.CharField(max_length=100)),
                ('description', models.CharField(max_length=250, null=True, blank=True)),
            ],
        ),
        migrations.AlterUniqueTogether(
            name='bookingsources',
            unique_together=set([('name', 'app_type')]),
        ),
        migrations.AddField(
            model_name='booking',
            name='padded_booked_range',
            field=django.contrib.postgres.fields.ranges.DateTimeRangeField(
                null=True, auto_created=True, blank=True
            ),
        ),
    ]
