import pytest
from dateutil.relativedelta import relativedelta
from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import SubbookingServiceVariantMode as SVMode
from webapps.booking.models import SubBooking
from webapps.business.models import Resource
from webapps.business.models import Service, ServiceVariant
from webapps.business.staffer_name_generator import generate_user_name
from webapps.pos.baker_recipes import default_pos_recipe


@pytest.mark.django_db
class MultibookingTest(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()

        self.staffer = baker.make(
            Resource,
            business=self.business,
            staff_user=self.business.owner,
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            name=generate_user_name(),
        )
        self.appliance = baker.make(
            Resource,
            business=self.business,
            active=True,
            visible=True,
            type=Resource.APPLIANCE,
        )

        # services
        self.service = baker.make(
            Service,
            business=self.business,
            active=True,
        )
        self.variant = baker.make(
            ServiceVariant,
            service=self.service,
            active=True,
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
        )
        self.service.add_staffers([self.staffer])

        default_pos_recipe.make()

    def test_multibooking_error(self):
        """
        Test fix for exception caused by `booked_for` being AnyResource
        https://redmine.booksy.pm/issues/62262
        """
        booking_data = {
            "_notify_about_reschedule": True,
            "_preserve_order": False,
            "_update_future_bookings": False,
            "_version": 0,
            "business_note": "",
            "business_secret_note": "",
            "customer": {
                "detailed_walkin": None,
                "email": None,
                "id": None,
                "invite": None,
                "mode": None,
                "name": None,
                "phone": None,
            },
            "customer_note": None,
            "dry_run": False,
            "new_repeating": None,
            "overbooking": False,
            "subbookings": [
                {
                    "appliance_id": None,
                    "booked_from": "2020-02-25T12:00",
                    "booked_till": "2020-02-25T12:40",
                    "duration": None,
                    "id": None,
                    "service_variant": {
                        "id": self.variant.id,
                        "mode": SVMode.VARIANT,
                        "service_name": None,
                    },
                    "staffer_id": self.staffer.id,
                },
                {
                    "appliance_id": None,
                    "booked_from": "2020-02-25T12:40",
                    "booked_till": "2020-02-25T14:10",
                    "duration": None,
                    "id": None,
                    "service_variant": {
                        "id": self.variant.id,
                        "mode": SVMode.VARIANT,
                        "service_name": None,
                    },
                    "staffer_id": self.staffer.id,
                },
            ],
        }

        url = f'/business_api/me/businesses/{self.business.id}/appointments/'
        response = self.fetch(url, method='POST', body=booking_data)
        assert response.code == 201
        assert SubBooking.objects.count() == 2
        for booking in SubBooking.objects.all():
            assert booking.appointment.booked_for is None
