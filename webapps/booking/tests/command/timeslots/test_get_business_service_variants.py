from datetime import date, datetime

import pytest
from mock import Mock, patch

from service.customer.timeslots import RequestData
from webapps.booking.commands.timeslots.get_business_service_variants import (
    BooksySlotsGenerator,
    GetBusinessServiceVariantsTimeSlotsCommand,
    GetBusinessServiceVariantsTimeSlotsCommandInput,
    TimeSlotsForward,
)
from webapps.booking.timeslots.v1.booksy_slots import BooksySlots
from webapps.business.baker_recipes import business_recipe


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2025, 3, 20))
@patch('webapps.booking.timeslots.v2.generator.requests.request')
@patch.object(BooksySlotsGenerator, "generate")
def test_booksy_business(generate_mock: Mock, _request_mock: Mock):
    today = date.today()
    sevice_variant_id = 123
    business = business_recipe.make()
    command = GetBusinessServiceVariantsTimeSlotsCommand()

    generate_mock.return_value = BooksySlots()

    command_output = command.execute(
        GetBusinessServiceVariantsTimeSlotsCommandInput(
            business_id=business.id,
            start=today,
            end=today,
            service_variant_ids=[sevice_variant_id],
        ),
    )

    assert command_output.slots == {
        sevice_variant_id: {},
    }


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime(2025, 3, 20))
@patch('webapps.booking.timeslots.v2.generator.requests.request')
@patch.object(RequestData, "to_slots_scope")
@patch.object(TimeSlotsForward, "should_forward")
def test_partner_business(
    should_forward_mock: Mock,
    to_slots_scope_mock: Mock,
    _request_mock: Mock,
):
    today = date.today()
    sevice_variant_id = 123
    business = business_recipe.make()
    command = GetBusinessServiceVariantsTimeSlotsCommand()

    should_forward_mock.return_value = True
    to_slots_scope_mock.return_value = None

    command_output = command.execute(
        GetBusinessServiceVariantsTimeSlotsCommandInput(
            business_id=business.id,
            start=today,
            end=today,
            service_variant_ids=[sevice_variant_id],
        ),
    )

    assert command_output.slots == {
        sevice_variant_id: {},
    }
