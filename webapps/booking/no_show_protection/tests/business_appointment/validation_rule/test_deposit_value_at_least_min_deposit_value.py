from decimal import Decimal

import pytest

from webapps.booking.no_show_protection.business_appointment.validation_rules.deposit_value_at_least_min_deposit_value import (  # pylint: disable=line-too-long
    DepositValueAtLeastMinDepositValue,
)


class TestDepositValueAtLeastMinDepositValue:

    @pytest.mark.parametrize(
        'deposit_value, expected_at_least_min',
        [
            (Dec<PERSON><PERSON>(4), False),
            (Decimal(5), True),
            (Decimal(190), True),
        ],
    )
    def test_is_valid__deposit_value_less_then_min(
        self, deposit_value: Decimal, expected_at_least_min: bool
    ):
        # given
        # when
        at_least_min = DepositValueAtLeastMinDepositValue(deposit_value).is_valid()
        # #then
        assert expected_at_least_min == at_least_min
