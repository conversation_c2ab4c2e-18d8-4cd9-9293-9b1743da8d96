from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import timedel<PERSON>
from typing import TYPE_CHECKING

from django.utils.functional import cached_property
from django.utils.timezone import get_current_timezone
from django.utils.translation import gettext_lazy as _

from lib.abc import abstractclassattribute
from lib.serializers import safe_get
from lib.tools import relativedelta_total_minutes
from webapps.booking.enums import WhoMakesChange
from webapps.booking.exceptions import MatcherConflict
from webapps.booking.timeslots.v1.time_scope import TimeRangesScope
from webapps.booking.tools.tools import (
    ExtraResources,
    get_existing_resource_ids,
    iter_leaf_services,
)
from webapps.business.models import Resource, ServiceVariant
from webapps.business.resources import AnyResource
from .consts import INFO, UPDATE_MANUAL

from .data_sources import get_custom_service_variant
from .functions import dt_from_slot, slot_from_dt
from .matcher import (
    MatchResult,
    MatchScope,
    MatchService,
    MatchServiceScope,
    ResultService,
)
from .matcher_availability import (
    BusinessAvailabilityMessages,
    CustomerAvailabilityMessages,
    Message,
    RequiredManualMessage,
)
from .matcher_turntracker import TurntrackerMixin
from .service_scope import InvalidScope, Service

if TYPE_CHECKING:
    from webapps.booking.models import Appointment, SubBooking
    from webapps.business.models import Business


class MatchScopeFactory(ABC):
    @abstractmethod
    def make(self, subbookings: list['SubBooking']): ...

    def service_from_subbooking(
        self, subbooking: dict, extra_resources: dict[int, ExtraResources]
    ) -> MatchService:
        """
        Create from subbooking data

        :param subbooking: validated data from request
        :param extra_resources: additional resources supported for given services
        :return:
        """
        service_variant: 'ServiceVariant' = subbooking.get('service_variant')
        staffer_id = safe_get(subbooking, ['staffer', 'id'])
        appliance_id = safe_get(subbooking, ['appliance', 'id'])

        if not service_variant:
            service_variant = get_custom_service_variant(
                staffer_ids=self.business.staffer_ids,
                appliance_ids=self.business.appliance_ids,
            )
            if not staffer_id:
                service_variant.staffer_ids = tuple()  # noqa
            if not appliance_id:
                service_variant.appliance_ids = tuple()  # noqa

        if staffer_id == AnyResource.id:
            staffer_id = None

        if appliance_id == AnyResource.id:
            appliance_id = None

        kwargs = {
            # from service_variant
            'interval': service_variant.interval_minutes,
            'padding_before': service_variant.padding_before_minutes,
            'padding_after': service_variant.padding_after_minutes,
            'gap_time': service_variant.gap_time_minutes,
            'staffer_ids': tuple(service_variant.staffer_ids),
            'appliance_ids': tuple(service_variant.appliance_ids),
            'combo_parallel': service_variant.combo_parallel,
            # from subbooking
            'duration': relativedelta_total_minutes(subbooking.get('duration')),
            'staffer_id': staffer_id,
            'appliance_id': appliance_id,
            'subbooking': subbooking,
            'combo_children': [
                self.service_from_subbooking(child, extra_resources)
                for child in subbooking.get('combo_children') or []
            ],
        }
        if booked_from := subbooking['booked_from']:
            kwargs['fixed_start'] = slot_from_dt(booked_from.astimezone(get_current_timezone()))

        extra_resources = extra_resources.get(service_variant.id)
        if extra_resources:
            kwargs['staffer_ids'] = tuple(set(kwargs['staffer_ids']) | extra_resources.staffer_ids)
            kwargs['appliance_ids'] = tuple(
                set(kwargs['appliance_ids']) | extra_resources.appliance_ids
            )

        match_service = MatchService(**kwargs)

        if service_variant.parallel_clients > 1:
            match_service.parallel_clients = Service.ParallelClients(
                service_id=service_variant.service_id,  # noqa
                count=service_variant.parallel_clients,
            )
        if (
            service_variant.gap_hole_start_after_minutes
            and service_variant.gap_hole_duration_minutes
        ):
            match_service.gap_hole = Service.GapHole(
                start_after=service_variant.gap_hole_start_after_minutes,
                duration=service_variant.gap_hole_duration_minutes,
            )

        return match_service


class MatchSubbookingsFactory(ABC):
    @abstractmethod
    def make(self, match_result: MatchResult): ...


class MatchDispatcher(ABC):
    ScopeFactory: MatchScopeFactory = abstractclassattribute()
    SubbookingsFactory: MatchSubbookingsFactory = abstractclassattribute()

    def __init__(self, **kwargs):
        # pylint: disable=not-callable
        self.scope_factory = self.ScopeFactory(**kwargs)  # noqa
        self.subbookings_factory = self.SubbookingsFactory(**kwargs)  # noqa
        # pylint: enable=not-callable

    def appointment_subbookings_match(self, *, subbookings, raise_on_conflict=False):
        from .use_cases import appointment_subbookings_match

        try:
            match_scope = self.scope_factory.make(subbookings)
        except InvalidScope as e:
            raise MatcherConflict(str(e)) from e

        match_result = appointment_subbookings_match(
            business=self.scope_factory.business,
            match_scope=match_scope,
        )

        if raise_on_conflict and any(service.conflict for service in match_result.services):
            raise MatcherConflict()

        return self.subbookings_factory.make(match_result)


class AppointmentMatchDispatcher(TurntrackerMixin, MatchDispatcher):
    @dataclass
    class ScopeFactory(MatchScopeFactory):
        business: 'Business'
        subbookings: list[dict]
        who_makes_change: WhoMakesChange
        existing_instance: 'Appointment' = None
        raise_on_conflict: bool = True

        def make(self, subbookings: list[dict]):
            service_scope = self._get_service_scope(
                subbookings,
                existing_instance=self.existing_instance,
            )
            time_scope = self._get_time_scope(subbookings, service_scope)

            omit_appointment_ids = [self.existing_instance.id] if self.existing_instance else None

            return MatchScope(
                service_scope=service_scope,
                time_scope=time_scope,
                omit_appointment_ids=omit_appointment_ids,
            )

        def _get_service_scope(self, subbookings, existing_instance: 'Appointment' = None):
            # business always can select any resource
            if self.who_makes_change == WhoMakesChange.BUSINESS:
                extra_resources = get_existing_resource_ids(subbookings)

            # customer can select resources already saved (ie. accepted) in an appointment
            if self.who_makes_change == WhoMakesChange.CUSTOMER:
                extra_resources = (
                    get_existing_resource_ids(existing_instance.subbookings)
                    if existing_instance
                    else {}
                )

            booked_from = min(
                subbooking['booked_from'] for subbooking in subbookings if subbooking['booked_from']
            )
            booked_from = booked_from.astimezone(get_current_timezone())
            start_slot = slot_from_dt(booked_from)
            preserve_order = self.who_makes_change == WhoMakesChange.BUSINESS

            validated_subbookings = list(
                self._validate_preserve_order(subbookings)
                if preserve_order
                else self._validate_for_reorder(subbookings)
            )
            services = tuple(
                self.service_from_subbooking(subbooking, extra_resources=extra_resources)  # noqa
                for subbooking in validated_subbookings
            )

            return MatchServiceScope(
                services=services,
                start_slot=start_slot,
                booked_from=booked_from,
                preserve_order=preserve_order,
            )

        def _validate_preserve_order(self, subbookings):
            """Ensure no fixed_start occurs after not fixed_start"""
            non_fixed_occured = False
            for subbooking in subbookings:
                if not subbooking['booked_from']:
                    non_fixed_occured = True
                yield clear_booked_from_till(subbooking) if non_fixed_occured else subbooking

        def _validate_for_reorder(self, subbookings):
            for subbooking in subbookings:
                yield clear_booked_from_till(subbooking)

        def _get_time_scope(self, subbookings, service_scope: MatchServiceScope):
            booked_from = subbookings[0]['booked_from']
            booked_from = booked_from.astimezone(get_current_timezone())

            padded_booked_from = booked_from - timedelta(
                minutes=service_scope.max_padding_before + 5
            )
            # make sure padding doesn't move to previous day
            if padded_booked_from.date() < booked_from.date():
                padded_booked_from = booked_from.replace(hour=0, minute=0)

            padded_end = padded_booked_from.replace(hour=23, minute=59).astimezone(
                get_current_timezone()
            )
            # this can occur when booked_from and padded_end cross DST change
            if padded_end.date() != padded_booked_from.date():
                padded_end = padded_end.replace(hour=0, minute=0) - timedelta(minutes=1)

            return TimeRangesScope(ranges=[(padded_booked_from, padded_end)])

    @dataclass
    class SubbookingsFactory(MatchSubbookingsFactory):
        business: 'Business'
        subbookings: list[dict]
        who_makes_change: WhoMakesChange
        existing_instance: 'Appointment' = None
        raise_on_conflict: bool = True

        def make(self, match_result: MatchResult):
            self.match_result = match_result
            return tuple(self._generate())

        @cached_property
        def availability_messages_class(self):
            if self.who_makes_change == WhoMakesChange.BUSINESS:
                return BusinessAvailabilityMessages
            return CustomerAvailabilityMessages

        def _booked_from_till(self, service: ResultService):
            booked_from = self.match_result.booked_from
            return {
                'booked_from': dt_from_slot(service.slot_from, booked_from),
                'booked_till': dt_from_slot(service.slot_till, booked_from),
            }

        def _resources_and_availability(self, service: ResultService):
            """See use_cases.appointment_subbookings_match doc about resources"""
            staffer = service.subbooking['staffer']
            appliance = service.subbooking['appliance']
            if not staffer or staffer is AnyResource:
                if service.staffer_id is not None:
                    staffer = self.find_resource(service.staffer_id, 'staffer')
                elif service.staffer_required:
                    staffer = AnyResource

            if not appliance or appliance is AnyResource:
                if service.appliance_id is not None:
                    appliance = self.find_resource(service.appliance_id, 'appliance')
                elif service.appliance_required:
                    appliance = AnyResource

            return {
                'staffer': staffer,
                'appliance': appliance,
                '_availability': service.availability.serialize(self.availability_messages_class),
            }

        def find_resource(self, resource_id, resource_type):
            """
            Find resource instance within subbookings

            For subbooking we need resource model instance.
            Matcher can set resource to one of the already selected resources in other subbookings,
            so we dont load/create resource instance, but find it.
            """
            for service in iter_leaf_services(self.match_result.services):
                try:
                    if service.subbooking[resource_type].id == resource_id:
                        return service.subbooking[resource_type]
                except AttributeError:
                    continue
            return Resource.objects.get(pk=resource_id)

        @staticmethod
        def _parallel_availability(service):
            availability = getattr(service, 'availability', None)
            if availability:
                return {'_availability': availability.serialize()}
            return {}

        def _generate(self):
            for service in self.match_result.services:
                if service.combo_children:
                    combo_children = {
                        'combo_children': tuple(
                            {
                                **child.subbooking,
                                **self._booked_from_till(child),
                                **self._resources_and_availability(child),
                            }
                            for child in service.combo_children
                        )
                    }
                    yield {
                        **service.subbooking,
                        **self._booked_from_till(service),
                        **self._parallel_availability(service),
                        **combo_children,
                    }
                else:
                    yield {
                        **service.subbooking,
                        **self._booked_from_till(service),
                        **self._resources_and_availability(service),
                    }


class GroupBookingMatchDispatcher(MatchDispatcher):
    @dataclass
    class ScopeFactory(AppointmentMatchDispatcher.ScopeFactory):
        group_booking_count: int = 0

        def make(self, subbookings: list[dict]):
            group_subbookings = self._make_parent_combo(subbookings[0])
            return super().make(group_subbookings)

        def _make_parent_combo(self, subbooking):
            parent_variant = ServiceVariant()
            child_variant = subbooking['service_variant'] or get_custom_service_variant(
                staffer_ids=self.business.staffer_ids,
                appliance_ids=self.business.appliance_ids,
            )
            setattr(parent_variant, 'combo_parallel', True)
            for attr in [
                'padding_before_minutes',
                'padding_after_minutes',
                'gap_time_minutes',
                'staffer_ids',
                'appliance_ids',
                'parallel_clients',
            ]:
                setattr(parent_variant, attr, getattr(child_variant, attr))

            child_booking = subbooking | {'staffer': AnyResource}
            parent_booking = {
                'booked_from': subbooking['booked_from'],
                'booked_till': subbooking['booked_till'],
                'service_variant': parent_variant,
                'combo_children': [subbooking] + [child_booking] * (self.group_booking_count - 1),
            }
            return [parent_booking]

    @dataclass
    class SubbookingsFactory(AppointmentMatchDispatcher.SubbookingsFactory):
        group_booking_count: int = 0

        class GroupBusinessAvailabilityMessages(BusinessAvailabilityMessages):
            messages = [
                (
                    message
                    if not isinstance(message, RequiredManualMessage)
                    else Message(
                        column=UPDATE_MANUAL,
                        type=INFO,
                        message=_('Manual update required'),
                    )
                )
                for message in BusinessAvailabilityMessages.messages
            ]

        @cached_property
        def availability_messages_class(self):
            return (
                self.GroupBusinessAvailabilityMessages
                if self.who_makes_change == WhoMakesChange.BUSINESS
                else CustomerAvailabilityMessages
            )

        def make(self, match_result: MatchResult):
            subbookings = super().make(match_result)
            return [subbookings[0]['combo_children'][0]]


def clear_booked_from_till(subbooking: dict):
    return subbooking | {
        'booked_from': None,
        'booked_till': None,
        'combo_children': [
            child | {'booked_from': None, 'booked_till': None}
            for child in subbooking.get('combo_children', [])
        ],
    }
