from dataclasses import dataclass
from datetime import timedelta

from django.utils.timezone import localdate

from webapps.booking.timeslots.v1.consts import SLOTS
from webapps.booking.timeslots.v1.promotions import (
    ResourceSlotsWithPromotions,
    SlotsWithPromotions,
)


@dataclass
class BooksySlots:
    merged_slots: SlotsWithPromotions | None = None
    staff_slots: ResourceSlotsWithPromotions | None = None

    @property
    def response(self):
        return {
            'time_slots': self.iter_slots(self.merged_slots) or [],
            'staff_time_slots': self.iter_staff_slots() or [],
        }

    @property
    def next_3_days_without_slots(self) -> bool:
        if self.merged_slots is None:
            return True

        today = localdate()
        near_dates = [today + timedelta(days=x) for x in range(3)]
        near_dates_slots = self.merged_slots.loc[self.merged_slots.index.isin(near_dates), SLOTS]
        not_available = not near_dates_slots.explode().any()

        return not_available

    @staticmethod
    def iter_slots(slots):
        if slots is None:
            return

        for date_, row in slots.iterrows():
            yield {
                'date': date_,
                'slots': [
                    {'slot': slot, 'promotion': promo}
                    for slot, promo in zip(row.slots, row.promotions)
                ],
                'working_day': row.working_day if hasattr(row, 'working_day') else None,
            }

    def iter_staff_slots(self):
        if self.staff_slots is None:
            return

        for resource_id in self.staff_slots.index.levels[0]:
            slots = self.staff_slots.loc[resource_id]
            yield {
                'staffer_id': resource_id,
                'time_slots': self.iter_slots(slots),
                'working_day': slots.working_day if hasattr(slots, 'working_day') else None,
            }
