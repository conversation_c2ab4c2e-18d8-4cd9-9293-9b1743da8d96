from dataclasses import asdict
from unittest.mock import patch, MagicMock, <PERSON><PERSON>

import pytest
from django.test import TestCase
from model_bakery import baker

from lib.point_of_sale.enums import BasketPaymentSource
from lib.point_of_sale.events import basket_payment_added_event
from webapps.business.models import Business
from webapps.french_certification.enums import FiscalReceiptType
from webapps.french_certification.exceptions import (
    PrepaymentForBasketExists,
    DifferentSumOfPrepaymentAmountsThanBasketPaymentAmount,
    PrepaymentExistsButNoFiscalReceipt,
)
from webapps.french_certification.models import FiscalReceipt
from webapps.french_certification.services.jet import JETService
from webapps.french_certification.services.loyalty_card import LoyaltyCardService
from webapps.french_certification.tests.common import get_fiscal_receipt_with_basket_data
from webapps.french_certification.tests.entities import TestBasketPaymentEntity
from webapps.french_certification.utils import (
    get_data_from_basket_item,
    get_data_from_basket_payment,
)
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.point_of_sale.models import Basket, BasketItem
from webapps.pos.models import Transaction


@pytest.mark.django_db
class TestFiscalReceiptForPrepayment(TestCase):
    def setUp(self):
        self.business = baker.make(Business)
        fc_seller_recipe.make(business=self.business)
        self.basket = baker.make(Basket, business_id=self.business.id)
        self.filter_mock_last = MagicMock()
        self.filter_mock = MagicMock()

    @classmethod
    def tearDownClass(cls):
        cls.mocked_french_certification_enabled.stop()
        cls.mocked_txn_refactor_stage2_enabled.stop()
        super().tearDownClass()

    @classmethod
    def setUpTestData(cls):
        cls.mocked_french_certification_enabled = patch(
            'webapps.french_certification.actions.french_certification_enabled',
            return_value=True,
        )
        cls.mocked_txn_refactor_stage2_enabled = patch(
            'webapps.french_certification.actions.txn_refactor_stage2_enabled',
            return_value=True,
        )
        cls.mocked_french_certification_enabled.start()
        cls.mocked_txn_refactor_stage2_enabled.start()

    def test_fiscal_receipt_for_prepayment_exists(self):
        get_fiscal_receipt_with_basket_data(
            business_id=self.basket.business_id, basket_id=self.basket.id, is_prepayment=True
        )
        basket_payment = TestBasketPaymentEntity(
            basket_id=self.basket.id, source=BasketPaymentSource.PREPAYMENT
        )

        with (
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
        ):
            with pytest.raises(PrepaymentForBasketExists):
                basket_payment_added_event.send(asdict(basket_payment))

    def test_different_sum_of_prepayment_amounts_than_basket_payment_amount(self):
        basket_item = baker.make(BasketItem, basket=self.basket, order=1)
        basket_payment = TestBasketPaymentEntity(
            basket_id=self.basket.id, amount=1000, source=BasketPaymentSource.PREPAYMENT
        )

        with (
            patch(
                'webapps.french_certification.actions.get_basket_items_data_for_prepayment',
                return_value={str(basket_item.id): {"prepayment_amount": 2000}},
            ) as mocked_prepayment_calculation,
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
        ):
            with pytest.raises(DifferentSumOfPrepaymentAmountsThanBasketPaymentAmount):
                basket_payment_added_event.send(asdict(basket_payment))

        mocked_prepayment_calculation.assert_called_once_with(basket_id=self.basket.id)

    def test_fiscal_receipt_for_prepayment_created(self):
        self.filter_mock_last.operator_id = 12345
        self.filter_mock_last.customer_card_id = None
        basket_item_1 = baker.make(
            BasketItem,
            basket=self.basket,
            gross_total=1200,
            net_total=1000,
            tax_amount=200,
            tax_rate=2000,
            order=1,
        )
        basket_item_2 = baker.make(
            BasketItem,
            basket=self.basket,
            gross_total=1100,
            net_total=1000,
            tax_amount=100,
            tax_rate=1000,
            order=1,
        )
        basket_payment = TestBasketPaymentEntity(
            basket_id=self.basket.id, amount=480, source=BasketPaymentSource.PREPAYMENT
        )

        with (
            patch(
                'webapps.french_certification.actions.get_basket_items_data_for_prepayment',
                return_value={
                    str(basket_item_1.id): get_data_from_basket_item(
                        basket_item=basket_item_1, prepayment_amount=240
                    ),
                    str(basket_item_2.id): get_data_from_basket_item(
                        basket_item=basket_item_2, prepayment_amount=240
                    ),
                },
            ) as mocked_prepayment_calculation,
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
        ):
            basket_payment_added_event.send(asdict(basket_payment))

        fiscal_receipts = FiscalReceipt.objects.filter(
            business_id=self.business.id,
            basket_id=self.basket.id,
        ).all()
        fiscal_receipt = fiscal_receipts[0]

        mocked_prepayment_calculation.assert_called_once_with(basket_id=self.basket.id)
        self.assertEqual(len(fiscal_receipts), 1)
        self.assertEqual(fiscal_receipt.basket_id, self.basket.id)
        self.assertEqual(fiscal_receipt.operator_id, 12345)
        self.assertIsNone(fiscal_receipt.bci_id)
        self.assertEqual(fiscal_receipt.type, FiscalReceiptType.SALE.value)
        self.assertTrue(fiscal_receipt.is_prepayment)
        self.assertSetEqual(
            set(fiscal_receipt.basket_item_ids), {basket_item_2.id, basket_item_1.id}
        )
        self.assertListEqual(fiscal_receipt.basket_payment_ids, [basket_payment.id])
        self.assertEqual(
            fiscal_receipt.basket_data,
            {
                'basket_items_data': {
                    str(basket_item_1.id): get_data_from_basket_item(
                        basket_item=basket_item_1,
                        prepayment_amount=240,
                    ),
                    str(basket_item_2.id): get_data_from_basket_item(
                        basket_item=basket_item_2,
                        prepayment_amount=240,
                    ),
                },
                'basket_payments_data': {
                    str(basket_payment.id): get_data_from_basket_payment(
                        basket_payment=basket_payment
                    )
                },
                'number_of_lines': 2,
                'prepayment_amount': 480,
            },
        )
        self.assertEqual(fiscal_receipt.total_items_amount, 2300)
        self.assertEqual(fiscal_receipt.total_paid_value, 480)
        self.assertListEqual(
            fiscal_receipt.tax_summary,
            [
                {'tax_rate': 2000, 'net_total': 200, 'tax_amount': 40, 'gross_total': 240},
                {'tax_rate': 1000, 'net_total': 218, 'tax_amount': 22, 'gross_total': 240},
            ],
        )

    def test_basket_payment_for_prepayment_but_no_receipt(self):
        JETService.data_initialization_event(business_id=self.business.id)
        self.filter_mock_last.customer_card_id = 12345
        baker.make(BasketItem, basket=self.basket, gross_total=1000)
        basket_payment_1 = TestBasketPaymentEntity(
            basket_id=self.basket.id, amount=500, source=BasketPaymentSource.PREPAYMENT
        )
        basket_payment_2 = TestBasketPaymentEntity(basket_id=self.basket.id, amount=500)

        with (
            patch.object(
                LoyaltyCardService, 'get_loyalty_card_transaction_data', return_value=True
            ),
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
            patch('webapps.french_certification.utils.total_value_paid', Mock(return_value=1000)),
            patch(
                'webapps.french_certification.actions.get_basket_payments',
                Mock(return_value=[basket_payment_1, basket_payment_2]),
            ),
        ):
            with pytest.raises(PrepaymentExistsButNoFiscalReceipt):
                basket_payment_added_event.send(asdict(basket_payment_2))

    def test_basket_payment_with_prepayment_before_cert(self):
        self.filter_mock_last.customer_card_id = 12345
        self.filter_mock_last.operator_id = 9876
        basket_item = baker.make(
            BasketItem,
            basket=self.basket,
            gross_total=1000,
            net_total=800,
            tax_amount=200,
            tax_rate=2500,
        )
        basket_payment_1 = TestBasketPaymentEntity(
            basket_id=self.basket.id, amount=500, source=BasketPaymentSource.PREPAYMENT
        )
        JETService.data_initialization_event(business_id=self.business.id)
        basket_payment_2 = TestBasketPaymentEntity(basket_id=self.basket.id, amount=500)

        with (
            patch.object(
                LoyaltyCardService,
                'get_loyalty_card_transaction_data',
                return_value={'bci_id': 12345},
            ),
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
            patch('webapps.french_certification.utils.total_value_paid', Mock(return_value=1000)),
            patch(
                'webapps.french_certification.actions.get_basket_payments',
                Mock(return_value=[basket_payment_1, basket_payment_2]),
            ),
            patch(
                'webapps.french_certification.utils.get_basket_payments',
                Mock(return_value=[basket_payment_1, basket_payment_2]),
            ),
            patch(
                'webapps.french_certification.utils.get_basket_items_data_for_prepayment',
                return_value={
                    str(basket_item.id): get_data_from_basket_item(
                        basket_item=basket_item, prepayment_amount=500
                    ),
                },
            ),
        ):
            basket_payment_added_event.send(asdict(basket_payment_2))

        final_fiscal_receipt = FiscalReceipt.objects.filter(
            business_id=self.business.id,
            basket_id=self.basket.id,
            is_prepayment=False,
        ).first()

        self.assertIsNotNone(final_fiscal_receipt)
        self.assertEqual(final_fiscal_receipt.basket_id, self.basket.id)
        self.assertEqual(final_fiscal_receipt.operator_id, 9876)
        self.assertEqual(final_fiscal_receipt.bci_id, 12345)
        self.assertEqual(final_fiscal_receipt.type, FiscalReceiptType.SALE.value)
        self.assertFalse(final_fiscal_receipt.is_prepayment)
        self.assertListEqual(final_fiscal_receipt.basket_item_ids, [basket_item.id])
        self.assertListEqual(final_fiscal_receipt.basket_payment_ids, [basket_payment_2.id])
        self.assertEqual(
            final_fiscal_receipt.basket_data,
            {
                'basket_items_data': {
                    str(basket_item.id): get_data_from_basket_item(
                        basket_item=basket_item,
                        prepayment_amount=0,
                    ),
                },
                'basket_payments_data': {
                    str(basket_payment_2.id): get_data_from_basket_payment(
                        basket_payment=basket_payment_2
                    )
                },
                'number_of_lines': 1,
                'prepayment_amount': 500,
            },
        )
        self.assertEqual(final_fiscal_receipt.total_items_amount, 1000)
        self.assertEqual(final_fiscal_receipt.total_paid_value, 500)
        self.assertListEqual(
            final_fiscal_receipt.tax_summary,
            [{'tax_rate': 2500, 'net_total': 400, 'tax_amount': 100, 'gross_total': 500}],
        )

    def test_basket_payment_with_previous_prepayment(self):
        JETService.data_initialization_event(business_id=self.business.id)
        self.filter_mock_last.customer_card_id = 12345
        self.filter_mock_last.operator_id = 9876
        basket_item = baker.make(
            BasketItem,
            basket=self.basket,
            gross_total=1000,
            net_total=800,
            tax_amount=200,
            tax_rate=2500,
        )
        basket_payment_1 = TestBasketPaymentEntity(
            basket_id=self.basket.id, amount=500, source=BasketPaymentSource.PREPAYMENT
        )
        prepayment = get_fiscal_receipt_with_basket_data(
            business_id=self.basket.business_id,
            basket_id=self.basket.id,
            basket_items=[basket_item],
            basket_payments=[basket_payment_1],
            is_prepayment=True,
            operator_id=8642,
            prepayment_amount=500,
        )
        basket_payment_2 = TestBasketPaymentEntity(basket_id=self.basket.id, amount=500)

        with (
            patch.object(
                LoyaltyCardService,
                'get_loyalty_card_transaction_data',
                return_value={'bci_id': 12345},
            ),
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
            patch('webapps.french_certification.utils.total_value_paid', Mock(return_value=1000)),
            patch(
                'webapps.french_certification.actions.get_basket_payments',
                Mock(return_value=[basket_payment_1, basket_payment_2]),
            ),
            patch(
                'webapps.french_certification.utils.get_basket_payments',
                Mock(return_value=[basket_payment_1, basket_payment_2]),
            ),
            patch(
                'webapps.french_certification.utils.get_basket_items_data_for_prepayment',
                return_value={
                    str(basket_item.id): get_data_from_basket_item(
                        basket_item=basket_item, prepayment_amount=500
                    ),
                },
            ),
        ):
            basket_payment_added_event.send(asdict(basket_payment_2))

        final_fiscal_receipt = FiscalReceipt.objects.filter(
            business_id=self.business.id,
            basket_id=self.basket.id,
            is_prepayment=False,
        ).first()

        self.assertEqual(prepayment.basket_id, self.basket.id)
        self.assertEqual(prepayment.operator_id, 8642)
        self.assertEqual(prepayment.type, FiscalReceiptType.SALE.value)
        self.assertTrue(prepayment.is_prepayment)
        self.assertListEqual(prepayment.basket_item_ids, [basket_item.id])
        self.assertListEqual(prepayment.basket_payment_ids, [basket_payment_1.id])
        self.assertIsNotNone(final_fiscal_receipt)
        self.assertEqual(final_fiscal_receipt.basket_id, self.basket.id)
        self.assertEqual(final_fiscal_receipt.operator_id, 9876)
        self.assertEqual(final_fiscal_receipt.bci_id, 12345)
        self.assertEqual(final_fiscal_receipt.type, FiscalReceiptType.SALE.value)
        self.assertFalse(final_fiscal_receipt.is_prepayment)
        self.assertListEqual(final_fiscal_receipt.basket_item_ids, [basket_item.id])
        self.assertListEqual(final_fiscal_receipt.basket_payment_ids, [basket_payment_2.id])
        self.assertEqual(
            final_fiscal_receipt.basket_data,
            {
                'basket_items_data': {
                    str(basket_item.id): get_data_from_basket_item(
                        basket_item=basket_item,
                        prepayment_amount=0,
                    ),
                },
                'basket_payments_data': {
                    str(basket_payment_2.id): get_data_from_basket_payment(
                        basket_payment=basket_payment_2
                    )
                },
                'number_of_lines': 1,
                'prepayment_amount': 500,
            },
        )
        self.assertEqual(final_fiscal_receipt.total_items_amount, 1000)
        self.assertEqual(final_fiscal_receipt.total_paid_value, 500)
        self.assertListEqual(
            final_fiscal_receipt.tax_summary,
            [{'tax_rate': 2500, 'net_total': 400, 'tax_amount': 100, 'gross_total': 500}],
        )

    def test_basket_payment_without_previous_prepayment(self):
        self.filter_mock_last.customer_card_id = 76398
        self.filter_mock_last.operator_id = 54326
        basket_item = baker.make(
            BasketItem,
            basket=self.basket,
            gross_total=5000,
            net_total=4000,
            tax_amount=1000,
        )
        basket_payment = TestBasketPaymentEntity(basket_id=self.basket.id, amount=5000)

        with (
            patch.object(
                LoyaltyCardService,
                'get_loyalty_card_transaction_data',
                return_value={'bci_id': 76398},
            ),
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
            patch('webapps.french_certification.utils.total_value_paid', Mock(return_value=5000)),
            patch(
                'webapps.french_certification.actions.get_basket_payments',
                Mock(return_value=[basket_payment]),
            ),
        ):
            basket_payment_added_event.send(asdict(basket_payment))
        fiscal_receipts = FiscalReceipt.objects.filter(
            business_id=self.business.id,
            basket_id=self.basket.id,
        ).all()
        final_fiscal_receipt = fiscal_receipts[0]

        self.assertEqual(len(fiscal_receipts), 1)
        self.assertIsNotNone(final_fiscal_receipt)
        self.assertEqual(final_fiscal_receipt.basket_id, self.basket.id)
        self.assertEqual(final_fiscal_receipt.operator_id, 54326)
        self.assertEqual(final_fiscal_receipt.bci_id, 76398)
        self.assertEqual(final_fiscal_receipt.type, FiscalReceiptType.SALE.value)
        self.assertFalse(final_fiscal_receipt.is_prepayment)
        self.assertListEqual(final_fiscal_receipt.basket_item_ids, [basket_item.id])
        self.assertListEqual(final_fiscal_receipt.basket_payment_ids, [basket_payment.id])

    def test_fiscal_receipt_is_created_for_cancellation_fee(self):
        self.filter_mock_last.customer_card_id = 76398
        self.filter_mock_last.operator_id = 54326
        basket_item = baker.make(
            BasketItem,
            basket=self.basket,
            item_price=5000,
            gross_total=5500,
            net_total=5000,
            tax_amount=500,
        )
        baker.make(BasketItem, basket=self.basket, item_price=0)
        basket_payment = TestBasketPaymentEntity(
            basket_id=self.basket.id, amount=5000, source=BasketPaymentSource.CANCELLATION_FEE
        )

        with (
            patch.object(
                Transaction.objects,
                'filter',
                return_value=self.filter_mock,
            ),
            patch.object(
                self.filter_mock,
                'last',
                return_value=self.filter_mock_last,
            ),
            patch(
                'webapps.french_certification.actions.get_basket_payments',
                Mock(return_value=[basket_payment]),
            ),
        ):
            basket_payment_added_event.send(asdict(basket_payment))
        fiscal_receipts = FiscalReceipt.objects.filter(
            business_id=self.business.id,
            basket_id=self.basket.id,
        ).all()
        fiscal_receipt = fiscal_receipts[0]
        self.assertEqual(len(fiscal_receipts), 1)
        self.assertEqual(fiscal_receipt.basket_id, self.basket.id)
        self.assertEqual(fiscal_receipt.operator_id, 54326)
        self.assertEqual(fiscal_receipt.bci_id, 76398)
        self.assertEqual(fiscal_receipt.type, FiscalReceiptType.SALE.value)
        self.assertFalse(fiscal_receipt.is_prepayment)
        self.assertListEqual(fiscal_receipt.basket_item_ids, [basket_item.id])
        self.assertListEqual(fiscal_receipt.basket_payment_ids, [basket_payment.id])
