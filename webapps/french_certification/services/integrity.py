from webapps.french_certification.services.jet import JETService
from webapps.french_certification.utils import verify_queryset_from_single_business


class IntegrityService:
    @staticmethod
    def verify_chain(queryset, **kwargs):
        verify_queryset_from_single_business(queryset)
        for item in queryset:
            if not item.is_signature_valid():
                JETService.integrity_failure_event(
                    business_id=item.business_id,
                    item=item,
                    operator_id=kwargs.get('operator_id'),
                )
