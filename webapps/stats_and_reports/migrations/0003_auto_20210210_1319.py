# Generated by Django 3.1.2 on 2021-02-10 13:19

from django.db import migrations
import lib.models
import webapps.pos.models


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0084_rename_bookings_to_subbookings'),
        ('sequencing_number', '0016_autoupdate_queryset_as_manager_67904'),
        ('pos', '0212_rename_multibooking_to_appointment'),
        ('stats_and_reports', '0002_create_subscriptions'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommodityCategoryReplica',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('warehouse.commoditycategory',),
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
        migrations.CreateModel(
            name='SequenceRecordReplica',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('sequencing_number.sequencerecord',),
        ),
    ]
