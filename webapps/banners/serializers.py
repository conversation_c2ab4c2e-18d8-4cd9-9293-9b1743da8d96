from rest_framework import serializers

from webapps.business_calendar.enums import CalendarBannerType
from webapps.business_consents.enums import ConsentCode

BANNER_SERIALIZER_HELP_TEXT = f'''
    additional_data schema depends on `banner` field.
    
    For {CalendarBannerType.PAYOUTS_AMOUNT} banner, the schema is, for example:
    {{
      "amount": "$200.00"
    }}
    
    For the rest, it is an empty object:
    {{}}
'''


class BannerSerializer(serializers.Serializer):
    banner = serializers.ChoiceField(choices=CalendarBannerType.choices())
    additional_data = serializers.JSONField(
        required=False, default=dict, help_text=BANNER_SERIALIZER_HELP_TEXT
    )


class ConsentCodeSerializer(serializers.Serializer):
    consent_code = serializers.ChoiceField(choices=ConsentCode.choices())
