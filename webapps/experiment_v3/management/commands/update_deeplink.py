from django.core.management import BaseCommand
from webapps.business.models import Business
from webapps.experiment_v3.management.commands.common import (
    read_and_print_deeplink_details,
)


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument('business_id', default=None)
        parser.add_argument('branchio_url', default=None)
        parser.add_argument('--check-and-print', action='store_true')

    def handle(self, *args, **opts):
        confirmation = input(
            "This command can work non-correct (`update_mp_deeplink_data`), "
            "are you sure you want to continue? (yes/no): "
        )
        if confirmation.lower() != 'yes':
            print("Command execution cancelled.")
            return

        business = Business.objects.filter(
            id=opts.get('business_id'),
        ).first()
        if not business:
            raise RuntimeError('Business not found')  # pylint: disable=broad-exception-raised
        branchio_url = opts.get('branchio_url')
        updated_url = business.update_mp_deeplink_data(
            deeplink_url=branchio_url,
        )
        print(f"{updated_url} updated")
        if not opts.get('check_and_print'):
            return
        read_and_print_deeplink_details(branchio_url)
