# Generated by Django 1.11.17 on 2019-01-24 15:41
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('pop_up_notification', '0019_auto_20190107_1202'),
    ]

    operations = [
        migrations.CreateModel(
            name='DigitalFlyerNotification',
            fields=[
                (
                    'genericpopupnotificationmodel_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='pop_up_notification.GenericPopUpNotificationModel',
                    ),
                ),
                (
                    'event_type',
                    models.CharField(
                        choices=[
                            ('5_star_review', '5 star review'),
                            ('canceled_appointment', 'canceled appointment'),
                            ('business_activated', 'business activated'),
                            ('first_cb', 'first cb'),
                        ],
                        max_length=64,
                    ),
                ),
                ('display_type', models.Char<PERSON>ield(max_length=64)),
                ('title', models.Char<PERSON>ield(max_length=64)),
                ('description', models.Char<PERSON>ield(max_length=64)),
                ('button_text', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=64, null=True)),
                ('df_category_id', models.IntegerField(blank=True, null=True)),
                ('df_background_id', models.IntegerField(blank=True, null=True)),
                ('df_text_id', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'abstract': False,
                'get_latest_by': 'updated',
            },
            bases=('pop_up_notification.genericpopupnotificationmodel',),
        ),
        migrations.AlterField(
            model_name='genericpopupnotificationmodel',
            name='notification_type',
            field=models.CharField(
                choices=[
                    ('meet_me_again', 'Meet me Again!'),
                    ('business_like', 'Business You may like'),
                    ('category_like', 'Category You may like'),
                    ('booking_pattern', ''),
                    ('short_review', 'Short Review'),
                    ('referral_c2b_reward_change', 'Reward C2B Status Change'),
                    ('late_cancellation', 'Late Cancellation'),
                    ('digital_flyer', 'Digital Flyer'),
                ],
                default='',
                max_length=30,
            ),
        ),
    ]
