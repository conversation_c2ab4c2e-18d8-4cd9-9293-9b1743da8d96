import datetime
import unittest

from webapps.b2b_acc_deletion.domain.models import (
    <PERSON><PERSON>,
    BusinessAddress,
    ContactInfo,
    SocialLinks,
    BusinessNames,
    BusinessDescriptions,
    UserAddress,
    UserData,
    BusinessRegion,
)
from webapps.b2b_acc_deletion.domain.services import AnonymizationService
from webapps.b2b_acc_deletion.shared.types import (
    EmailAddress,
    PhotoId,
    PhoneNumber,
    RegionId,
    StafferId,
)


class TestAnonymizationService(unittest.TestCase):
    def setUp(self):
        self.anonymization_service = AnonymizationService()

    def test_anonymize_staffer(self):
        staffer = Staffer(
            staffer_id=StafferId(1),
            name="<PERSON>",
            email=EmailAddress("<EMAIL>"),
            phone=PhoneNumber("1234567890"),
            photo_id=PhotoId(1),
        )
        anonymized_staffer = self.anonymization_service.anonymize_data(staffer)

        self.assertNotEqual(anonymized_staffer.name, staffer.name)
        self.assertNotEqual(anonymized_staffer.email, staffer.email)
        self.assertEqual(anonymized_staffer.staffer_id, staffer.staffer_id)
        self.assertEqual(anonymized_staffer.phone, "")
        self.assertIsNone(anonymized_staffer.photo_id)

    def test_anonymize_business_address(self):
        address = BusinessAddress("123 Main St", "Apt 4", "12345", "City", 0.0, 0.0)
        anonymized_address = self.anonymization_service.anonymize_data(address)

        self.assertIsNone(anonymized_address.address)
        self.assertIsNone(anonymized_address.address2)
        self.assertIsNone(anonymized_address.zipcode)
        self.assertNotEqual(anonymized_address.city, address.city)
        self.assertIsNone(anonymized_address.longitude)
        self.assertIsNone(anonymized_address.latitude)

    def test_anonymize_contact_info(self):
        contact_info = ContactInfo(
            PhoneNumber("1234567890"),
            PhoneNumber("0987654321"),
            EmailAddress("<EMAIL>"),
            EmailAddress("<EMAIL>"),
        )
        anonymized_contact_info = self.anonymization_service.anonymize_data(contact_info)

        self.assertEqual(anonymized_contact_info.phone, "")
        self.assertEqual(anonymized_contact_info.alert_phone, "")
        self.assertEqual(anonymized_contact_info.source_email, "")
        self.assertIsNone(anonymized_contact_info.public_email)

    def test_anonymize_social_links(self):
        social_links = SocialLinks("website.com", "facebook.com", "instagram.com", "shop.com")
        anonymized_social_links = self.anonymization_service.anonymize_data(social_links)

        self.assertIsNone(anonymized_social_links.website)
        self.assertIsNone(anonymized_social_links.facebook_link)
        self.assertIsNone(anonymized_social_links.instagram_link)
        self.assertIsNone(anonymized_social_links.ecommerce_link)

    def test_anonymize_business_names(self):
        business_names = BusinessNames(
            "Business Name", "Short Name", "Official Name", "Umbrella Brand"
        )
        anonymized_business_names = self.anonymization_service.anonymize_data(business_names)

        self.assertNotEqual(anonymized_business_names.name, business_names.name)
        self.assertNotEqual(anonymized_business_names.name_short, business_names.name_short)
        self.assertNotEqual(anonymized_business_names.official_name, business_names.official_name)
        self.assertNotEqual(
            anonymized_business_names.umbrella_brand_name, business_names.umbrella_brand_name
        )

    def test_anonymize_business_descriptions(self):
        business_descriptions = BusinessDescriptions("Description", "Contractor Description")
        anonymized_business_descriptions = self.anonymization_service.anonymize_data(
            business_descriptions
        )

        self.assertIsNone(anonymized_business_descriptions.description)
        self.assertIsNone(anonymized_business_descriptions.contractor_description)

    def test_anonymize_user_address(self):
        user_address = UserAddress(
            "123 Main St", "Apt 4", "5", "City", "12345", 0.0, 0.0, RegionId(1)
        )
        anonymized_user_address = self.anonymization_service.anonymize_data(user_address)

        self.assertNotEqual(anonymized_user_address.address_line_1, user_address.address_line_1)
        self.assertNotEqual(anonymized_user_address.address_line_2, user_address.address_line_2)
        self.assertEqual(anonymized_user_address.apartment_number, "")
        self.assertIsNone(anonymized_user_address.city)
        self.assertIsNone(anonymized_user_address.zipcode)
        self.assertIsNone(anonymized_user_address.latitude)
        self.assertIsNone(anonymized_user_address.longitude)
        self.assertIsNone(anonymized_user_address.region_id)

    def test_anonymize_user_data(self):
        user_data = UserData(
            EmailAddress('<EMAIL>'),
            'password123',
            'John',
            'Doe',
            'John Doe',
            datetime.date(1994, 1, 30),
            'facebook id',
            'google id',
            PhoneNumber('1234599890'),
            PhoneNumber('1234566890'),
            PhoneNumber('1234544890'),
            'apple_user_uuid',
            'authenticator_code',
        )
        anonymized_user_data = self.anonymization_service.anonymize_data(user_data)

        self.assertNotEqual(anonymized_user_data.email, user_data.email)
        self.assertNotEqual(anonymized_user_data.password, user_data.password)
        self.assertNotEqual(anonymized_user_data.first_name, user_data.first_name)
        self.assertNotEqual(anonymized_user_data.last_name, user_data.last_name)
        self.assertNotEqual(anonymized_user_data.username, user_data.username)

        self.assertIsNone(anonymized_user_data.birthday)
        self.assertIsNone(anonymized_user_data.facebook_id)
        self.assertIsNone(anonymized_user_data.google_id)
        self.assertIsNone(anonymized_user_data.apple_user_uuid)

        self.assertEqual(anonymized_user_data.cell_phone, '')
        self.assertEqual(anonymized_user_data.home_phone, '')
        self.assertEqual(anonymized_user_data.work_phone, '')

    def test_anonymize_business_region(self):
        business_region = BusinessRegion(
            region_id=RegionId(1),
            name='22233',
            display_name='22233',
            longitude=1.0,
            latitude=2.0,
        )
        anonymized_business_region = self.anonymization_service.anonymize_data(business_region)

        self.assertEqual(anonymized_business_region.name, '')
        self.assertEqual(anonymized_business_region.display_name, '')
        self.assertIsNone(anonymized_business_region.longitude)
        self.assertIsNone(anonymized_business_region.latitude)
