# Generated by Django 3.1.13 on 2021-08-25 15:45

from django.db import migrations, models
import webapps.marketplace.cms.enums


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0162_feature_flag_is_active_to_active'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='seocontent',
            name='one_active_position_per_type_in_category',
        ),
        migrations.RemoveConstraint(
            model_name='seocontent',
            name='one_active_position_for_recommended4u',
        ),
        migrations.RemoveConstraint(
            model_name='seocontentdata',
            name='one_active_entry_per_language',
        ),
        migrations.RenameField(
            model_name='seocontent',
            old_name='is_active',
            new_name='active',
        ),
        migrations.RenameField(
            model_name='seocontentdata',
            old_name='is_active',
            new_name='active',
        ),
        migrations.AddConstraint(
            model_name='seocontent',
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ('active', True),
                    models.Q(
                        _negated=True,
                        content_type=webapps.marketplace.cms.enums.SeoCmsContentType[
                            'RECOMMENDED4U'
                        ],
                    ),
                ),
                fields=('category', 'position', 'active'),
                name='one_active_position_per_type_in_category',
            ),
        ),
        migrations.AddConstraint(
            model_name='seocontent',
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ('active', True),
                    ('category__isnull', True),
                    (
                        'content_type',
                        webapps.marketplace.cms.enums.SeoCmsContentType['RECOMMENDED4U'],
                    ),
                ),
                fields=('position', 'active'),
                name='one_active_position_for_recommended4u',
            ),
        ),
        migrations.AddConstraint(
            model_name='seocontentdata',
            constraint=models.UniqueConstraint(
                condition=models.Q(active=True),
                fields=('seo_content', 'language', 'active'),
                name='one_active_entry_per_language',
            ),
        ),
    ]
